<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Sun Jul 27 00:12:59 CST 2025 -->
<title>所有类和接口</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-07-27">
<meta name="description" content="class index">
<meta name="generator" content="javadoc/AllClassesIndexWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="all-classes-index-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html#all-classes">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="所有类和接口" class="title">所有类和接口</h1>
</div>
<div id="all-classes-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="all-classes-table-tab0" role="tab" aria-selected="true" aria-controls="all-classes-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table', 2)" class="active-table-tab">所有类和接口</button><button id="all-classes-table-tab2" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab2', 2)" class="table-tab">类</button><button id="all-classes-table-tab3" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab3', 2)" class="table-tab">枚举类</button></div>
<div id="all-classes-table.tabpanel" role="tabpanel" aria-labelledby="all-classes-table-tab0">
<div class="summary-table two-column-summary">
<div class="table-header col-first">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="mclmli/operationdiamondv2main/device/DeviceDetector.html" title="mclmli.operationdiamondv2main.device中的类">DeviceDetector</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">简化设备检测器 - 只检测基岩版/Java版和触屏/非触屏</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab3"><a href="mclmli/operationdiamondv2main/device/DeviceDetector.DeviceType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.DeviceType</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab3">
<div class="block">设备类型枚举 - 简化版</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab3"><a href="mclmli/operationdiamondv2main/device/DeviceDetector.InputType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.InputType</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab3">
<div class="block">输入方式枚举 - 简化版</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="mclmli/operationdiamondv2main/device/DeviceDetector.PlayerDeviceInfo.html" title="mclmli.operationdiamondv2main.device中的类">DeviceDetector.PlayerDeviceInfo</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">玩家设备信息类 - 简化版</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="mclmli/operationdiamondv2main/device/DeviceManager.html" title="mclmli.operationdiamondv2main.device中的类">DeviceManager</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">设备管理器 - 管理所有玩家的设备信息
 不依赖于特定功能模块，为整个插件提供设备检测服务</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="mclmli/operationdiamondv2main/OperationDiamondv2_Main.html" title="mclmli.operationdiamondv2main中的类">OperationDiamondv2_Main</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="mclmli/operationdiamondv2main/team/Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">队伍数据类
 包含队伍的所有信息和成员管理</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="mclmli/operationdiamondv2main/team/TeamApplication.html" title="mclmli.operationdiamondv2main.team中的类">TeamApplication</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">队伍申请数据类
 处理队伍申请相关信息</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab3"><a href="mclmli/operationdiamondv2main/team/TeamApplication.ApplicationStatus.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamApplication.ApplicationStatus</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab3">
<div class="block">申请状态枚举</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="mclmli/operationdiamondv2main/team/TeamCommand.html" title="mclmli.operationdiamondv2main.team中的类">TeamCommand</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">组队指令处理器
 处理所有组队相关的指令</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="mclmli/operationdiamondv2main/team/TeamDeviceAnalyzer.html" title="mclmli.operationdiamondv2main.team中的类">TeamDeviceAnalyzer</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">队伍设备组成分析器
 用于分析队伍中玩家的设备类型组成，判断队伍类型</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="mclmli/operationdiamondv2main/team/TeamDeviceAnalyzer.DeviceComposition.html" title="mclmli.operationdiamondv2main.team中的类">TeamDeviceAnalyzer.DeviceComposition</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">设备组成分析结果</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab3"><a href="mclmli/operationdiamondv2main/team/TeamDeviceRestriction.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamDeviceRestriction</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab3">
<div class="block">队伍设备限制枚举
 定义队伍允许加入的设备类型限制</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab3"><a href="mclmli/operationdiamondv2main/team/TeamDeviceType.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamDeviceType</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab3">
<div class="block">队伍设备类型枚举
 基于队伍成员的设备组成来分类队伍类型</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="mclmli/operationdiamondv2main/team/TeamInvitation.html" title="mclmli.operationdiamondv2main.team中的类">TeamInvitation</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">队伍邀请数据类
 处理队伍邀请相关信息</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="mclmli/operationdiamondv2main/team/TeamJoinValidator.html" title="mclmli.operationdiamondv2main.team中的类">TeamJoinValidator</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">队伍加入验证和警告系统
 处理玩家加入队伍时的设备类型验证和警告消息</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="mclmli/operationdiamondv2main/team/TeamJoinValidator.JoinValidationResult.html" title="mclmli.operationdiamondv2main.team中的类">TeamJoinValidator.JoinValidationResult</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">加入验证结果类</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="mclmli/operationdiamondv2main/team/TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">组队管理器 - 负责处理所有组队相关的逻辑
 支持Java版和基岩版玩家的跨平台组队功能
 集成Geyser和Floodgate API提供完整的跨平台支持</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab3"><a href="mclmli/operationdiamondv2main/team/TeamManager.JoinResult.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamManager.JoinResult</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab3">
<div class="block">加入队伍的结果枚举</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab3"><a href="mclmli/operationdiamondv2main/team/TeamStatus.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamStatus</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab3">
<div class="block">队伍状态枚举
 定义队伍的不同访问和加入模式</div>
</div>
</div>
</div>
</div>
</main>
</div>
</div>
</body>
</html>
