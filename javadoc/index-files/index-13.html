<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Sun Jul 27 00:12:59 CST 2025 -->
<title>O - 索引</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-07-27">
<meta name="description" content="index: O">
<meta name="generator" content="javadoc/IndexWriter">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
<script type="text/javascript" src="../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../script-dir/jquery-ui.min.js"></script>
</head>
<body class="index-page">
<script type="text/javascript">var pathtoroot = "../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li class="nav-bar-cell1-rev">索引</li>
<li><a href="../help-doc.html#index">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1>索引</h1>
</div>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">M</a>&nbsp;<a href="index-12.html">N</a>&nbsp;<a href="index-13.html">O</a>&nbsp;<a href="index-14.html">P</a>&nbsp;<a href="index-15.html">R</a>&nbsp;<a href="index-16.html">S</a>&nbsp;<a href="index-17.html">T</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<a href="index-19.html">W</a>&nbsp;<br><a href="../allpackages-index.html">所有程序包</a><span class="vertical-separator">|</span><a href="../allclasses-index.html">所有类和接口</a>
<h2 class="title" id="I:O">O</h2>
<dl class="index">
<dt><a href="../mclmli/operationdiamondv2main/team/TeamCommand.html#onCommand(org.bukkit.command.CommandSender,org.bukkit.command.Command,java.lang.String,java.lang.String%5B%5D)" class="member-name-link">onCommand(CommandSender, Command, String, String[])</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamCommand.html" title="mclmli.operationdiamondv2main.team中的类">TeamCommand</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/OperationDiamondv2_Main.html#onDisable()" class="member-name-link">onDisable()</a> - 类中的方法 mclmli.operationdiamondv2main.<a href="../mclmli/operationdiamondv2main/OperationDiamondv2_Main.html" title="mclmli.operationdiamondv2main中的类">OperationDiamondv2_Main</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/OperationDiamondv2_Main.html#onEnable()" class="member-name-link">onEnable()</a> - 类中的方法 mclmli.operationdiamondv2main.<a href="../mclmli/operationdiamondv2main/OperationDiamondv2_Main.html" title="mclmli.operationdiamondv2main中的类">OperationDiamondv2_Main</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/device/DeviceManager.html#onPlayerJoin(org.bukkit.event.player.PlayerJoinEvent)" class="member-name-link">onPlayerJoin(PlayerJoinEvent)</a> - 类中的方法 mclmli.operationdiamondv2main.device.<a href="../mclmli/operationdiamondv2main/device/DeviceManager.html" title="mclmli.operationdiamondv2main.device中的类">DeviceManager</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/device/DeviceManager.html#onPlayerQuit(org.bukkit.event.player.PlayerQuitEvent)" class="member-name-link">onPlayerQuit(PlayerQuitEvent)</a> - 类中的方法 mclmli.operationdiamondv2main.device.<a href="../mclmli/operationdiamondv2main/device/DeviceManager.html" title="mclmli.operationdiamondv2main.device中的类">DeviceManager</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamCommand.html#onTabComplete(org.bukkit.command.CommandSender,org.bukkit.command.Command,java.lang.String,java.lang.String%5B%5D)" class="member-name-link">onTabComplete(CommandSender, Command, String, String[])</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamCommand.html" title="mclmli.operationdiamondv2main.team中的类">TeamCommand</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamStatus.html#OPEN" class="member-name-link">OPEN</a> - 枚举类 中的枚举常量 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamStatus.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamStatus</a></dt>
<dd>
<div class="block">公开 - 任何人都可以直接加入</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/OperationDiamondv2_Main.html" class="type-name-link" title="mclmli.operationdiamondv2main中的类">OperationDiamondv2_Main</a> - <a href="../mclmli/operationdiamondv2main/package-summary.html">mclmli.operationdiamondv2main</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/OperationDiamondv2_Main.html#%3Cinit%3E()" class="member-name-link">OperationDiamondv2_Main()</a> - 类的构造器 mclmli.operationdiamondv2main.<a href="../mclmli/operationdiamondv2main/OperationDiamondv2_Main.html" title="mclmli.operationdiamondv2main中的类">OperationDiamondv2_Main</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">M</a>&nbsp;<a href="index-12.html">N</a>&nbsp;<a href="index-13.html">O</a>&nbsp;<a href="index-14.html">P</a>&nbsp;<a href="index-15.html">R</a>&nbsp;<a href="index-16.html">S</a>&nbsp;<a href="index-17.html">T</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<a href="index-19.html">W</a>&nbsp;<br><a href="../allpackages-index.html">所有程序包</a><span class="vertical-separator">|</span><a href="../allclasses-index.html">所有类和接口</a></main>
</div>
</div>
</body>
</html>
