<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Sun Jul 27 00:12:59 CST 2025 -->
<title>T - 索引</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-07-27">
<meta name="description" content="index: T">
<meta name="generator" content="javadoc/IndexWriter">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
<script type="text/javascript" src="../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../script-dir/jquery-ui.min.js"></script>
</head>
<body class="index-page">
<script type="text/javascript">var pathtoroot = "../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li class="nav-bar-cell1-rev">索引</li>
<li><a href="../help-doc.html#index">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1>索引</h1>
</div>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">M</a>&nbsp;<a href="index-12.html">N</a>&nbsp;<a href="index-13.html">O</a>&nbsp;<a href="index-14.html">P</a>&nbsp;<a href="index-15.html">R</a>&nbsp;<a href="index-16.html">S</a>&nbsp;<a href="index-17.html">T</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<a href="index-19.html">W</a>&nbsp;<br><a href="../allpackages-index.html">所有程序包</a><span class="vertical-separator">|</span><a href="../allclasses-index.html">所有类和接口</a>
<h2 class="title" id="I:T">T</h2>
<dl class="index">
<dt><a href="../mclmli/operationdiamondv2main/team/Team.html" class="type-name-link" title="mclmli.operationdiamondv2main.team中的类">Team</a> - <a href="../mclmli/operationdiamondv2main/team/package-summary.html">mclmli.operationdiamondv2main.team</a>中的类</dt>
<dd>
<div class="block">队伍数据类
 包含队伍的所有信息和成员管理</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/Team.html#%3Cinit%3E(java.lang.String,java.lang.String,java.util.UUID)" class="member-name-link">Team(String, String, UUID)</a> - 类的构造器 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></dt>
<dd>
<div class="block">创建新队伍</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamManager.JoinResult.html#TEAM_FULL" class="member-name-link">TEAM_FULL</a> - 枚举类 中的枚举常量 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamManager.JoinResult.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamManager.JoinResult</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamManager.JoinResult.html#TEAM_NOT_FOUND" class="member-name-link">TEAM_NOT_FOUND</a> - 枚举类 中的枚举常量 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamManager.JoinResult.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamManager.JoinResult</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamApplication.html" class="type-name-link" title="mclmli.operationdiamondv2main.team中的类">TeamApplication</a> - <a href="../mclmli/operationdiamondv2main/team/package-summary.html">mclmli.operationdiamondv2main.team</a>中的类</dt>
<dd>
<div class="block">队伍申请数据类
 处理队伍申请相关信息</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamApplication.html#%3Cinit%3E(java.lang.String,java.util.UUID,java.lang.String)" class="member-name-link">TeamApplication(String, UUID, String)</a> - 类的构造器 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamApplication.html" title="mclmli.operationdiamondv2main.team中的类">TeamApplication</a></dt>
<dd>
<div class="block">创建队伍申请</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamApplication.ApplicationStatus.html" class="type-name-link" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamApplication.ApplicationStatus</a> - <a href="../mclmli/operationdiamondv2main/team/package-summary.html">mclmli.operationdiamondv2main.team</a>中的枚举类</dt>
<dd>
<div class="block">申请状态枚举</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamCommand.html" class="type-name-link" title="mclmli.operationdiamondv2main.team中的类">TeamCommand</a> - <a href="../mclmli/operationdiamondv2main/team/package-summary.html">mclmli.operationdiamondv2main.team</a>中的类</dt>
<dd>
<div class="block">组队指令处理器
 处理所有组队相关的指令</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamCommand.html#%3Cinit%3E(mclmli.operationdiamondv2main.team.TeamManager,mclmli.operationdiamondv2main.device.DeviceManager,mclmli.operationdiamondv2main.team.TeamGUIManager,mclmli.operationdiamondv2main.team.TeamFormManager)" class="member-name-link">TeamCommand(TeamManager, DeviceManager, TeamGUIManager, TeamFormManager)</a> - 类的构造器 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamCommand.html" title="mclmli.operationdiamondv2main.team中的类">TeamCommand</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamDeviceAnalyzer.html" class="type-name-link" title="mclmli.operationdiamondv2main.team中的类">TeamDeviceAnalyzer</a> - <a href="../mclmli/operationdiamondv2main/team/package-summary.html">mclmli.operationdiamondv2main.team</a>中的类</dt>
<dd>
<div class="block">队伍设备组成分析器
 用于分析队伍中玩家的设备类型组成，判断队伍类型</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamDeviceAnalyzer.html#%3Cinit%3E(mclmli.operationdiamondv2main.team.TeamManager)" class="member-name-link">TeamDeviceAnalyzer(TeamManager)</a> - 类的构造器 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamDeviceAnalyzer.html" title="mclmli.operationdiamondv2main.team中的类">TeamDeviceAnalyzer</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamDeviceAnalyzer.DeviceComposition.html" class="type-name-link" title="mclmli.operationdiamondv2main.team中的类">TeamDeviceAnalyzer.DeviceComposition</a> - <a href="../mclmli/operationdiamondv2main/team/package-summary.html">mclmli.operationdiamondv2main.team</a>中的类</dt>
<dd>
<div class="block">设备组成分析结果</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamDeviceRestriction.html" class="type-name-link" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamDeviceRestriction</a> - <a href="../mclmli/operationdiamondv2main/team/package-summary.html">mclmli.operationdiamondv2main.team</a>中的枚举类</dt>
<dd>
<div class="block">队伍设备限制枚举
 定义队伍允许加入的设备类型限制</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamDeviceType.html" class="type-name-link" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamDeviceType</a> - <a href="../mclmli/operationdiamondv2main/team/package-summary.html">mclmli.operationdiamondv2main.team</a>中的枚举类</dt>
<dd>
<div class="block">队伍设备类型枚举
 基于队伍成员的设备组成来分类队伍类型</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamInvitation.html" class="type-name-link" title="mclmli.operationdiamondv2main.team中的类">TeamInvitation</a> - <a href="../mclmli/operationdiamondv2main/team/package-summary.html">mclmli.operationdiamondv2main.team</a>中的类</dt>
<dd>
<div class="block">队伍邀请数据类
 处理队伍邀请相关信息</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamInvitation.html#%3Cinit%3E(java.lang.String,java.util.UUID,java.util.UUID)" class="member-name-link">TeamInvitation(String, UUID, UUID)</a> - 类的构造器 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamInvitation.html" title="mclmli.operationdiamondv2main.team中的类">TeamInvitation</a></dt>
<dd>
<div class="block">创建队伍邀请</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamJoinValidator.html" class="type-name-link" title="mclmli.operationdiamondv2main.team中的类">TeamJoinValidator</a> - <a href="../mclmli/operationdiamondv2main/team/package-summary.html">mclmli.operationdiamondv2main.team</a>中的类</dt>
<dd>
<div class="block">队伍加入验证和警告系统
 处理玩家加入队伍时的设备类型验证和警告消息</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamJoinValidator.html#%3Cinit%3E(mclmli.operationdiamondv2main.team.TeamManager)" class="member-name-link">TeamJoinValidator(TeamManager)</a> - 类的构造器 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamJoinValidator.html" title="mclmli.operationdiamondv2main.team中的类">TeamJoinValidator</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamJoinValidator.JoinValidationResult.html" class="type-name-link" title="mclmli.operationdiamondv2main.team中的类">TeamJoinValidator.JoinValidationResult</a> - <a href="../mclmli/operationdiamondv2main/team/package-summary.html">mclmli.operationdiamondv2main.team</a>中的类</dt>
<dd>
<div class="block">加入验证结果类</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamManager.html" class="type-name-link" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a> - <a href="../mclmli/operationdiamondv2main/team/package-summary.html">mclmli.operationdiamondv2main.team</a>中的类</dt>
<dd>
<div class="block">组队管理器 - 负责处理所有组队相关的逻辑
 支持Java版和基岩版玩家的跨平台组队功能
 集成Geyser和Floodgate API提供完整的跨平台支持</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamManager.html#%3Cinit%3E()" class="member-name-link">TeamManager()</a> - 类的构造器 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamManager.JoinResult.html" class="type-name-link" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamManager.JoinResult</a> - <a href="../mclmli/operationdiamondv2main/team/package-summary.html">mclmli.operationdiamondv2main.team</a>中的枚举类</dt>
<dd>
<div class="block">加入队伍的结果枚举</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamStatus.html" class="type-name-link" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamStatus</a> - <a href="../mclmli/operationdiamondv2main/team/package-summary.html">mclmli.operationdiamondv2main.team</a>中的枚举类</dt>
<dd>
<div class="block">队伍状态枚举
 定义队伍的不同访问和加入模式</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/Team.html#toString()" class="member-name-link">toString()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamApplication.html#toString()" class="member-name-link">toString()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamApplication.html" title="mclmli.operationdiamondv2main.team中的类">TeamApplication</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamInvitation.html#toString()" class="member-name-link">toString()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamInvitation.html" title="mclmli.operationdiamondv2main.team中的类">TeamInvitation</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/device/DeviceDetector.InputType.html#TOUCHSCREEN" class="member-name-link">TOUCHSCREEN</a> - 枚举类 中的枚举常量 mclmli.operationdiamondv2main.device.<a href="../mclmli/operationdiamondv2main/device/DeviceDetector.InputType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.InputType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamDeviceRestriction.html#TOUCHSCREEN_ONLY" class="member-name-link">TOUCHSCREEN_ONLY</a> - 枚举类 中的枚举常量 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamDeviceRestriction.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamDeviceRestriction</a></dt>
<dd>
<div class="block">仅限触屏 - 只允许触屏玩家加入</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamDeviceType.html#TOUCHSCREEN_ONLY" class="member-name-link">TOUCHSCREEN_ONLY</a> - 枚举类 中的枚举常量 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamDeviceType.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamDeviceType</a></dt>
<dd>
<div class="block">仅触屏玩家队伍
 队伍中所有玩家都是使用触屏操作的玩家</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/Team.html#transferLeadership(java.util.UUID)" class="member-name-link">transferLeadership(UUID)</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></dt>
<dd>
<div class="block">转移队长权限</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">M</a>&nbsp;<a href="index-12.html">N</a>&nbsp;<a href="index-13.html">O</a>&nbsp;<a href="index-14.html">P</a>&nbsp;<a href="index-15.html">R</a>&nbsp;<a href="index-16.html">S</a>&nbsp;<a href="index-17.html">T</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<a href="index-19.html">W</a>&nbsp;<br><a href="../allpackages-index.html">所有程序包</a><span class="vertical-separator">|</span><a href="../allclasses-index.html">所有类和接口</a></main>
</div>
</div>
</body>
</html>
