<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Sun Jul 27 00:12:59 CST 2025 -->
<title>G - 索引</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-07-27">
<meta name="description" content="index: G">
<meta name="generator" content="javadoc/IndexWriter">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
<script type="text/javascript" src="../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../script-dir/jquery-ui.min.js"></script>
</head>
<body class="index-page">
<script type="text/javascript">var pathtoroot = "../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li class="nav-bar-cell1-rev">索引</li>
<li><a href="../help-doc.html#index">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1>索引</h1>
</div>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">M</a>&nbsp;<a href="index-12.html">N</a>&nbsp;<a href="index-13.html">O</a>&nbsp;<a href="index-14.html">P</a>&nbsp;<a href="index-15.html">R</a>&nbsp;<a href="index-16.html">S</a>&nbsp;<a href="index-17.html">T</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<a href="index-19.html">W</a>&nbsp;<br><a href="../allpackages-index.html">所有程序包</a><span class="vertical-separator">|</span><a href="../allclasses-index.html">所有类和接口</a>
<h2 class="title" id="I:G">G</h2>
<dl class="index">
<dt><a href="../mclmli/operationdiamondv2main/team/TeamManager.html#getAllTeams()" class="member-name-link">getAllTeams()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a></dt>
<dd>
<div class="block">获取所有队伍列表</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamApplication.html#getApplicantId()" class="member-name-link">getApplicantId()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamApplication.html" title="mclmli.operationdiamondv2main.team中的类">TeamApplication</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamApplication.html#getApplicationId()" class="member-name-link">getApplicationId()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamApplication.html" title="mclmli.operationdiamondv2main.team中的类">TeamApplication</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamApplication.html#getApplicationTime()" class="member-name-link">getApplicationTime()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamApplication.html" title="mclmli.operationdiamondv2main.team中的类">TeamApplication</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamManager.html#getBedrockConnection(java.util.UUID)" class="member-name-link">getBedrockConnection(UUID)</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a></dt>
<dd>
<div class="block">获取基岩版玩家的连接信息</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/device/DeviceManager.html#getCacheStatistics()" class="member-name-link">getCacheStatistics()</a> - 类中的方法 mclmli.operationdiamondv2main.device.<a href="../mclmli/operationdiamondv2main/device/DeviceManager.html" title="mclmli.operationdiamondv2main.device中的类">DeviceManager</a></dt>
<dd>
<div class="block">获取设备缓存的统计信息</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamDeviceAnalyzer.DeviceComposition.html#getCompositionDescription()" class="member-name-link">getCompositionDescription()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamDeviceAnalyzer.DeviceComposition.html" title="mclmli.operationdiamondv2main.team中的类">TeamDeviceAnalyzer.DeviceComposition</a></dt>
<dd>
<div class="block">获取组成描述</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/Team.html#getCreateTime()" class="member-name-link">getCreateTime()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamDeviceRestriction.html#getDefault()" class="member-name-link">getDefault()</a> - 枚举类中的静态方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamDeviceRestriction.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamDeviceRestriction</a></dt>
<dd>
<div class="block">获取默认的设备限制模式</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamDeviceRestriction.html#getDescription()" class="member-name-link">getDescription()</a> - 枚举类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamDeviceRestriction.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamDeviceRestriction</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamDeviceType.html#getDescription()" class="member-name-link">getDescription()</a> - 枚举类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamDeviceType.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamDeviceType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamStatus.html#getDescription()" class="member-name-link">getDescription()</a> - 枚举类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamStatus.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamStatus</a></dt>
<dd>
<div class="block">获取状态描述</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamDeviceAnalyzer.DeviceComposition.html#getDetailedDeviceList()" class="member-name-link">getDetailedDeviceList()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamDeviceAnalyzer.DeviceComposition.html" title="mclmli.operationdiamondv2main.team中的类">TeamDeviceAnalyzer.DeviceComposition</a></dt>
<dd>
<div class="block">获取详细设备列表</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/device/DeviceDetector.PlayerDeviceInfo.html#getDetailedInfo()" class="member-name-link">getDetailedInfo()</a> - 类中的方法 mclmli.operationdiamondv2main.device.<a href="../mclmli/operationdiamondv2main/device/DeviceDetector.PlayerDeviceInfo.html" title="mclmli.operationdiamondv2main.device中的类">DeviceDetector.PlayerDeviceInfo</a></dt>
<dd>
<div class="block">获取详细信息</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamDeviceAnalyzer.DeviceComposition.html#getDeviceInfos()" class="member-name-link">getDeviceInfos()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamDeviceAnalyzer.DeviceComposition.html" title="mclmli.operationdiamondv2main.team中的类">TeamDeviceAnalyzer.DeviceComposition</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/OperationDiamondv2_Main.html#getDeviceManager()" class="member-name-link">getDeviceManager()</a> - 类中的方法 mclmli.operationdiamondv2main.<a href="../mclmli/operationdiamondv2main/OperationDiamondv2_Main.html" title="mclmli.operationdiamondv2main中的类">OperationDiamondv2_Main</a></dt>
<dd>
<div class="block">获取设备管理器实例</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/device/DeviceManager.html#getDevicePrefix(mclmli.operationdiamondv2main.device.DeviceDetector.PlayerDeviceInfo)" class="member-name-link">getDevicePrefix(DeviceDetector.PlayerDeviceInfo)</a> - 类中的方法 mclmli.operationdiamondv2main.device.<a href="../mclmli/operationdiamondv2main/device/DeviceManager.html" title="mclmli.operationdiamondv2main.device中的类">DeviceManager</a></dt>
<dd>
<div class="block">获取设备类型对应的前缀</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/device/DeviceDetector.html#getDeviceRecommendations(mclmli.operationdiamondv2main.device.DeviceDetector.PlayerDeviceInfo)" class="member-name-link">getDeviceRecommendations(DeviceDetector.PlayerDeviceInfo)</a> - 类中的静态方法 mclmli.operationdiamondv2main.device.<a href="../mclmli/operationdiamondv2main/device/DeviceDetector.html" title="mclmli.operationdiamondv2main.device中的类">DeviceDetector</a></dt>
<dd>
<div class="block">根据设备类型推荐功能</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/Team.html#getDeviceRestriction()" class="member-name-link">getDeviceRestriction()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/device/DeviceDetector.PlayerDeviceInfo.html#getDeviceType()" class="member-name-link">getDeviceType()</a> - 类中的方法 mclmli.operationdiamondv2main.device.<a href="../mclmli/operationdiamondv2main/device/DeviceDetector.PlayerDeviceInfo.html" title="mclmli.operationdiamondv2main.device中的类">DeviceDetector.PlayerDeviceInfo</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/device/DeviceDetector.DeviceType.html#getDisplayName()" class="member-name-link">getDisplayName()</a> - 枚举类中的方法 mclmli.operationdiamondv2main.device.<a href="../mclmli/operationdiamondv2main/device/DeviceDetector.DeviceType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.DeviceType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/device/DeviceDetector.InputType.html#getDisplayName()" class="member-name-link">getDisplayName()</a> - 枚举类中的方法 mclmli.operationdiamondv2main.device.<a href="../mclmli/operationdiamondv2main/device/DeviceDetector.InputType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.InputType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamApplication.ApplicationStatus.html#getDisplayName()" class="member-name-link">getDisplayName()</a> - 枚举类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamApplication.ApplicationStatus.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamApplication.ApplicationStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamDeviceRestriction.html#getDisplayName()" class="member-name-link">getDisplayName()</a> - 枚举类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamDeviceRestriction.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamDeviceRestriction</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamDeviceType.html#getDisplayName()" class="member-name-link">getDisplayName()</a> - 枚举类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamDeviceType.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamDeviceType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamStatus.html#getDisplayName()" class="member-name-link">getDisplayName()</a> - 枚举类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamStatus.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamStatus</a></dt>
<dd>
<div class="block">获取状态显示名称</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamDeviceType.html#getDisplayNameWithEmoji()" class="member-name-link">getDisplayNameWithEmoji()</a> - 枚举类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamDeviceType.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamDeviceType</a></dt>
<dd>
<div class="block">获取带图标的显示名称</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/device/DeviceDetector.DeviceType.html#getEmoji()" class="member-name-link">getEmoji()</a> - 枚举类中的方法 mclmli.operationdiamondv2main.device.<a href="../mclmli/operationdiamondv2main/device/DeviceDetector.DeviceType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.DeviceType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/device/DeviceDetector.InputType.html#getEmoji()" class="member-name-link">getEmoji()</a> - 枚举类中的方法 mclmli.operationdiamondv2main.device.<a href="../mclmli/operationdiamondv2main/device/DeviceDetector.InputType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.InputType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamDeviceType.html#getEmoji()" class="member-name-link">getEmoji()</a> - 枚举类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamDeviceType.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamDeviceType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamApplication.html#getExpireTime()" class="member-name-link">getExpireTime()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamApplication.html" title="mclmli.operationdiamondv2main.team中的类">TeamApplication</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamInvitation.html#getExpireTime()" class="member-name-link">getExpireTime()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamInvitation.html" title="mclmli.operationdiamondv2main.team中的类">TeamInvitation</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamManager.html#getFloodgatePlayer(java.util.UUID)" class="member-name-link">getFloodgatePlayer(UUID)</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a></dt>
<dd>
<div class="block">获取基岩版玩家的Floodgate信息</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/device/DeviceDetector.PlayerDeviceInfo.html#getFullDescription()" class="member-name-link">getFullDescription()</a> - 类中的方法 mclmli.operationdiamondv2main.device.<a href="../mclmli/operationdiamondv2main/device/DeviceDetector.PlayerDeviceInfo.html" title="mclmli.operationdiamondv2main.device中的类">DeviceDetector.PlayerDeviceInfo</a></dt>
<dd>
<div class="block">获取完整描述</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamDeviceRestriction.html#getFullDescription()" class="member-name-link">getFullDescription()</a> - 枚举类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamDeviceRestriction.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamDeviceRestriction</a></dt>
<dd>
<div class="block">获取带描述的完整显示</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/device/DeviceDetector.PlayerDeviceInfo.html#getInputType()" class="member-name-link">getInputType()</a> - 类中的方法 mclmli.operationdiamondv2main.device.<a href="../mclmli/operationdiamondv2main/device/DeviceDetector.PlayerDeviceInfo.html" title="mclmli.operationdiamondv2main.device中的类">DeviceDetector.PlayerDeviceInfo</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamInvitation.html#getInvitationId()" class="member-name-link">getInvitationId()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamInvitation.html" title="mclmli.operationdiamondv2main.team中的类">TeamInvitation</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamInvitation.html#getInviteeId()" class="member-name-link">getInviteeId()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamInvitation.html" title="mclmli.operationdiamondv2main.team中的类">TeamInvitation</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamInvitation.html#getInviterId()" class="member-name-link">getInviterId()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamInvitation.html" title="mclmli.operationdiamondv2main.team中的类">TeamInvitation</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamInvitation.html#getInviteTime()" class="member-name-link">getInviteTime()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamInvitation.html" title="mclmli.operationdiamondv2main.team中的类">TeamInvitation</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/Team.html#getLeaderId()" class="member-name-link">getLeaderId()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/Team.html#getMaxMembers()" class="member-name-link">getMaxMembers()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/Team.html#getMemberCount()" class="member-name-link">getMemberCount()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></dt>
<dd>
<div class="block">获取当前队伍人数</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/Team.html#getMembers()" class="member-name-link">getMembers()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamApplication.html#getMessage()" class="member-name-link">getMessage()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamApplication.html" title="mclmli.operationdiamondv2main.team中的类">TeamApplication</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamJoinValidator.JoinValidationResult.html#getMessage()" class="member-name-link">getMessage()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamJoinValidator.JoinValidationResult.html" title="mclmli.operationdiamondv2main.team中的类">TeamJoinValidator.JoinValidationResult</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamDeviceAnalyzer.DeviceComposition.html#getNonTouchscreenCount()" class="member-name-link">getNonTouchscreenCount()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamDeviceAnalyzer.DeviceComposition.html" title="mclmli.operationdiamondv2main.team中的类">TeamDeviceAnalyzer.DeviceComposition</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamManager.html#getOnlineBedrockPlayerCount()" class="member-name-link">getOnlineBedrockPlayerCount()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a></dt>
<dd>
<div class="block">获取在线基岩版玩家数量</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/device/DeviceManager.html#getOrDetectPlayerDeviceInfo(org.bukkit.entity.Player)" class="member-name-link">getOrDetectPlayerDeviceInfo(Player)</a> - 类中的方法 mclmli.operationdiamondv2main.device.<a href="../mclmli/operationdiamondv2main/device/DeviceManager.html" title="mclmli.operationdiamondv2main.device中的类">DeviceManager</a></dt>
<dd>
<div class="block">获取在线玩家的设备信息，如果缓存中没有则重新检测</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/Team.html#getPassword()" class="member-name-link">getPassword()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/device/DeviceManager.html#getPlayerDeviceInfo(java.util.UUID)" class="member-name-link">getPlayerDeviceInfo(UUID)</a> - 类中的方法 mclmli.operationdiamondv2main.device.<a href="../mclmli/operationdiamondv2main/device/DeviceManager.html" title="mclmli.operationdiamondv2main.device中的类">DeviceManager</a></dt>
<dd>
<div class="block">获取玩家的设备信息</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamManager.html#getPlayerFullDeviceInfo(java.util.UUID)" class="member-name-link">getPlayerFullDeviceInfo(UUID)</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a></dt>
<dd>
<div class="block">获取玩家完整设备信息</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/device/DeviceDetector.PlayerDeviceInfo.html#getPlayerId()" class="member-name-link">getPlayerId()</a> - 类中的方法 mclmli.operationdiamondv2main.device.<a href="../mclmli/operationdiamondv2main/device/DeviceDetector.PlayerDeviceInfo.html" title="mclmli.operationdiamondv2main.device中的类">DeviceDetector.PlayerDeviceInfo</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamManager.html#getPlayerInvitation(java.util.UUID)" class="member-name-link">getPlayerInvitation(UUID)</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a></dt>
<dd>
<div class="block">获取玩家的邀请</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamManager.html#getPlayerPlatformInfo(java.util.UUID)" class="member-name-link">getPlayerPlatformInfo(UUID)</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a></dt>
<dd>
<div class="block">获取基岩版玩家的详细信息</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamManager.html#getPlayerTeam(java.util.UUID)" class="member-name-link">getPlayerTeam(UUID)</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a></dt>
<dd>
<div class="block">获取玩家所在的队伍</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamManager.html#getPublicTeams()" class="member-name-link">getPublicTeams()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a></dt>
<dd>
<div class="block">获取所有公开的队伍列表（用于GUI显示）</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/device/DeviceDetector.PlayerDeviceInfo.html#getRawDeviceOs()" class="member-name-link">getRawDeviceOs()</a> - 类中的方法 mclmli.operationdiamondv2main.device.<a href="../mclmli/operationdiamondv2main/device/DeviceDetector.PlayerDeviceInfo.html" title="mclmli.operationdiamondv2main.device中的类">DeviceDetector.PlayerDeviceInfo</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/device/DeviceDetector.PlayerDeviceInfo.html#getRawInputMode()" class="member-name-link">getRawInputMode()</a> - 类中的方法 mclmli.operationdiamondv2main.device.<a href="../mclmli/operationdiamondv2main/device/DeviceDetector.PlayerDeviceInfo.html" title="mclmli.operationdiamondv2main.device中的类">DeviceDetector.PlayerDeviceInfo</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/device/DeviceDetector.PlayerDeviceInfo.html#getShortDescription()" class="member-name-link">getShortDescription()</a> - 类中的方法 mclmli.operationdiamondv2main.device.<a href="../mclmli/operationdiamondv2main/device/DeviceDetector.PlayerDeviceInfo.html" title="mclmli.operationdiamondv2main.device中的类">DeviceDetector.PlayerDeviceInfo</a></dt>
<dd>
<div class="block">获取简短描述</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamApplication.html#getShortDescription()" class="member-name-link">getShortDescription()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamApplication.html" title="mclmli.operationdiamondv2main.team中的类">TeamApplication</a></dt>
<dd>
<div class="block">获取申请的简短描述</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/Team.html#getStatus()" class="member-name-link">getStatus()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamApplication.html#getStatus()" class="member-name-link">getStatus()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamApplication.html" title="mclmli.operationdiamondv2main.team中的类">TeamApplication</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamManager.html#getTeam(java.lang.String)" class="member-name-link">getTeam(String)</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a></dt>
<dd>
<div class="block">根据ID获取队伍</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamManager.html#getTeamApplication(java.lang.String,java.util.UUID)" class="member-name-link">getTeamApplication(String, UUID)</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a></dt>
<dd>
<div class="block">获取队伍的申请</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/Team.html#getTeamDeviceType(mclmli.operationdiamondv2main.device.DeviceManager)" class="member-name-link">getTeamDeviceType(DeviceManager)</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></dt>
<dd>
<div class="block">获取队伍设备类型</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/OperationDiamondv2_Main.html#getTeamFormManager()" class="member-name-link">getTeamFormManager()</a> - 类中的方法 mclmli.operationdiamondv2main.<a href="../mclmli/operationdiamondv2main/OperationDiamondv2_Main.html" title="mclmli.operationdiamondv2main中的类">OperationDiamondv2_Main</a></dt>
<dd>
<div class="block">获取表单管理器实例</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/OperationDiamondv2_Main.html#getTeamGUIManager()" class="member-name-link">getTeamGUIManager()</a> - 类中的方法 mclmli.operationdiamondv2main.<a href="../mclmli/operationdiamondv2main/OperationDiamondv2_Main.html" title="mclmli.operationdiamondv2main中的类">OperationDiamondv2_Main</a></dt>
<dd>
<div class="block">获取GUI管理器实例</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/Team.html#getTeamId()" class="member-name-link">getTeamId()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamApplication.html#getTeamId()" class="member-name-link">getTeamId()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamApplication.html" title="mclmli.operationdiamondv2main.team中的类">TeamApplication</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamInvitation.html#getTeamId()" class="member-name-link">getTeamId()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamInvitation.html" title="mclmli.operationdiamondv2main.team中的类">TeamInvitation</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/OperationDiamondv2_Main.html#getTeamManager()" class="member-name-link">getTeamManager()</a> - 类中的方法 mclmli.operationdiamondv2main.<a href="../mclmli/operationdiamondv2main/OperationDiamondv2_Main.html" title="mclmli.operationdiamondv2main中的类">OperationDiamondv2_Main</a></dt>
<dd>
<div class="block">获取组队管理器实例</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/Team.html#getTeamName()" class="member-name-link">getTeamName()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamDeviceAnalyzer.DeviceComposition.html#getTeamType()" class="member-name-link">getTeamType()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamDeviceAnalyzer.DeviceComposition.html" title="mclmli.operationdiamondv2main.team中的类">TeamDeviceAnalyzer.DeviceComposition</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamDeviceAnalyzer.DeviceComposition.html#getTotalCount()" class="member-name-link">getTotalCount()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamDeviceAnalyzer.DeviceComposition.html" title="mclmli.operationdiamondv2main.team中的类">TeamDeviceAnalyzer.DeviceComposition</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamDeviceAnalyzer.DeviceComposition.html#getTouchscreenCount()" class="member-name-link">getTouchscreenCount()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamDeviceAnalyzer.DeviceComposition.html" title="mclmli.operationdiamondv2main.team中的类">TeamDeviceAnalyzer.DeviceComposition</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">M</a>&nbsp;<a href="index-12.html">N</a>&nbsp;<a href="index-13.html">O</a>&nbsp;<a href="index-14.html">P</a>&nbsp;<a href="index-15.html">R</a>&nbsp;<a href="index-16.html">S</a>&nbsp;<a href="index-17.html">T</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<a href="index-19.html">W</a>&nbsp;<br><a href="../allpackages-index.html">所有程序包</a><span class="vertical-separator">|</span><a href="../allclasses-index.html">所有类和接口</a></main>
</div>
</div>
</body>
</html>
