<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Sun Jul 27 00:12:59 CST 2025 -->
<title>I - 索引</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-07-27">
<meta name="description" content="index: I">
<meta name="generator" content="javadoc/IndexWriter">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
<script type="text/javascript" src="../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../script-dir/jquery-ui.min.js"></script>
</head>
<body class="index-page">
<script type="text/javascript">var pathtoroot = "../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="../overview-tree.html">树</a></li>
<li class="nav-bar-cell1-rev">索引</li>
<li><a href="../help-doc.html#index">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1>索引</h1>
</div>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">M</a>&nbsp;<a href="index-12.html">N</a>&nbsp;<a href="index-13.html">O</a>&nbsp;<a href="index-14.html">P</a>&nbsp;<a href="index-15.html">R</a>&nbsp;<a href="index-16.html">S</a>&nbsp;<a href="index-17.html">T</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<a href="index-19.html">W</a>&nbsp;<br><a href="../allpackages-index.html">所有程序包</a><span class="vertical-separator">|</span><a href="../allclasses-index.html">所有类和接口</a>
<h2 class="title" id="I:I">I</h2>
<dl class="index">
<dt><a href="../mclmli/operationdiamondv2main/team/TeamManager.html#initialize()" class="member-name-link">initialize()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a></dt>
<dd>
<div class="block">初始化组队管理器</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamManager.JoinResult.html#INVITE_ONLY" class="member-name-link">INVITE_ONLY</a> - 枚举类 中的枚举常量 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamManager.JoinResult.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamManager.JoinResult</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamStatus.html#INVITE_ONLY" class="member-name-link">INVITE_ONLY</a> - 枚举类 中的枚举常量 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamStatus.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamStatus</a></dt>
<dd>
<div class="block">仅邀请 - 只有被邀请的玩家才能加入</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamJoinValidator.JoinValidationResult.html#isAllowed()" class="member-name-link">isAllowed()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamJoinValidator.JoinValidationResult.html" title="mclmli.operationdiamondv2main.team中的类">TeamJoinValidator.JoinValidationResult</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../mclmli/operationdiamondv2main/device/DeviceDetector.PlayerDeviceInfo.html#isBedrockPlayer()" class="member-name-link">isBedrockPlayer()</a> - 类中的方法 mclmli.operationdiamondv2main.device.<a href="../mclmli/operationdiamondv2main/device/DeviceDetector.PlayerDeviceInfo.html" title="mclmli.operationdiamondv2main.device中的类">DeviceDetector.PlayerDeviceInfo</a></dt>
<dd>
<div class="block">是否为基岩版玩家</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamManager.html#isBedrockPlayer(java.util.UUID)" class="member-name-link">isBedrockPlayer(UUID)</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a></dt>
<dd>
<div class="block">检查玩家是否为基岩版玩家
 优先使用Floodgate API，如果不可用则使用Geyser API</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamManager.html#isBedrockPlayer(org.bukkit.entity.Player)" class="member-name-link">isBedrockPlayer(Player)</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a></dt>
<dd>
<div class="block">检查玩家是否为基岩版玩家
 优先使用Floodgate API，如果不可用则使用Geyser API</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/Team.html#isDeviceAllowed(java.util.UUID,mclmli.operationdiamondv2main.device.DeviceManager)" class="member-name-link">isDeviceAllowed(UUID, DeviceManager)</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></dt>
<dd>
<div class="block">检查玩家设备是否允许加入队伍</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamApplication.html#isExpired()" class="member-name-link">isExpired()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamApplication.html" title="mclmli.operationdiamondv2main.team中的类">TeamApplication</a></dt>
<dd>
<div class="block">检查申请是否过期</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamInvitation.html#isExpired()" class="member-name-link">isExpired()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamInvitation.html" title="mclmli.operationdiamondv2main.team中的类">TeamInvitation</a></dt>
<dd>
<div class="block">检查邀请是否过期</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamManager.html#isFloodgateEnabled()" class="member-name-link">isFloodgateEnabled()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a></dt>
<dd>
<div class="block">检查Floodgate是否启用</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/Team.html#isFull()" class="member-name-link">isFull()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></dt>
<dd>
<div class="block">检查队伍是否已满</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamManager.html#isGeyserEnabled()" class="member-name-link">isGeyserEnabled()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a></dt>
<dd>
<div class="block">检查Geyser是否启用</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/Team.html#isLeader(java.util.UUID)" class="member-name-link">isLeader(UUID)</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></dt>
<dd>
<div class="block">检查玩家是否是队长</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/Team.html#isMember(java.util.UUID)" class="member-name-link">isMember(UUID)</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></dt>
<dd>
<div class="block">检查玩家是否是队员</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamManager.html#isTeamNameExists(java.lang.String)" class="member-name-link">isTeamNameExists(String)</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a></dt>
<dd>
<div class="block">检查队伍名称是否已存在</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/device/DeviceDetector.PlayerDeviceInfo.html#isTouchInput()" class="member-name-link">isTouchInput()</a> - 类中的方法 mclmli.operationdiamondv2main.device.<a href="../mclmli/operationdiamondv2main/device/DeviceDetector.PlayerDeviceInfo.html" title="mclmli.operationdiamondv2main.device中的类">DeviceDetector.PlayerDeviceInfo</a></dt>
<dd>
<div class="block">是否使用触屏操作</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/Team.html#isTouchscreenOnlyTeam(mclmli.operationdiamondv2main.device.DeviceManager)" class="member-name-link">isTouchscreenOnlyTeam(DeviceManager)</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></dt>
<dd>
<div class="block">检查队伍是否是纯触屏队伍</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamDeviceAnalyzer.html#isTouchscreenPlayer(mclmli.operationdiamondv2main.device.DeviceDetector.PlayerDeviceInfo)" class="member-name-link">isTouchscreenPlayer(DeviceDetector.PlayerDeviceInfo)</a> - 类中的静态方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamDeviceAnalyzer.html" title="mclmli.operationdiamondv2main.team中的类">TeamDeviceAnalyzer</a></dt>
<dd>
<div class="block">判断玩家是否为触屏玩家</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamApplication.html#isValid()" class="member-name-link">isValid()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamApplication.html" title="mclmli.operationdiamondv2main.team中的类">TeamApplication</a></dt>
<dd>
<div class="block">检查申请是否有效（未过期且处于待处理状态）</div>
</dd>
<dt><a href="../mclmli/operationdiamondv2main/team/TeamInvitation.html#isValid()" class="member-name-link">isValid()</a> - 类中的方法 mclmli.operationdiamondv2main.team.<a href="../mclmli/operationdiamondv2main/team/TeamInvitation.html" title="mclmli.operationdiamondv2main.team中的类">TeamInvitation</a></dt>
<dd>
<div class="block">检查邀请是否有效</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">M</a>&nbsp;<a href="index-12.html">N</a>&nbsp;<a href="index-13.html">O</a>&nbsp;<a href="index-14.html">P</a>&nbsp;<a href="index-15.html">R</a>&nbsp;<a href="index-16.html">S</a>&nbsp;<a href="index-17.html">T</a>&nbsp;<a href="index-18.html">V</a>&nbsp;<a href="index-19.html">W</a>&nbsp;<br><a href="../allpackages-index.html">所有程序包</a><span class="vertical-separator">|</span><a href="../allclasses-index.html">所有类和接口</a></main>
</div>
</div>
</body>
</html>
