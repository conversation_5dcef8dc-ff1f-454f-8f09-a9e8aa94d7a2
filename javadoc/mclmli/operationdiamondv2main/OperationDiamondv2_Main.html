<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Sun Jul 27 00:12:59 CST 2025 -->
<title>OperationDiamondv2_Main</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-07-27">
<meta name="description" content="declaration: package: mclmli.operationdiamondv2main, class: OperationDiamondv2_Main">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
<script type="text/javascript" src="../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="class-use/OperationDiamondv2_Main.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../index-files/index-1.html">索引</a></li>
<li><a href="../../help-doc.html#class">帮助</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>概要:</p>
<ul>
<li>嵌套</li>
<li>字段</li>
<li><a href="#constructor-summary">构造器</a></li>
<li><a href="#method-summary">方法</a></li>
</ul>
</li>
<li>
<p>详细资料:</p>
<ul>
<li>字段</li>
<li><a href="#constructor-detail">构造器</a></li>
<li><a href="#method-detail">方法</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">mclmli.operationdiamondv2main</a></div>
<h1 title="类 OperationDiamondv2_Main" class="title">类 OperationDiamondv2_Main</h1>
</div>
<div class="inheritance" title="继承树"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance">org.bukkit.plugin.PluginBase
<div class="inheritance">org.bukkit.plugin.java.JavaPlugin
<div class="inheritance">mclmli.operationdiamondv2main.OperationDiamondv2_Main</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>所有已实现的接口:</dt>
<dd><code>io.papermc.paper.plugin.lifecycle.event.LifecycleEventOwner</code>, <code>org.bukkit.command.CommandExecutor</code>, <code>org.bukkit.command.TabCompleter</code>, <code>org.bukkit.command.TabExecutor</code>, <code>org.bukkit.plugin.Plugin</code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public final class </span><span class="element-name type-name-label">OperationDiamondv2_Main</span>
<span class="extends-implements">extends org.bukkit.plugin.java.JavaPlugin</span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>构造器概要</h2>
<div class="caption"><span>构造器</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">构造器</div>
<div class="table-header col-last">说明</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">OperationDiamondv2_Main</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="device/DeviceManager.html" title="mclmli.operationdiamondv2main.device中的类">DeviceManager</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDeviceManager()" class="member-name-link">getDeviceManager</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取设备管理器实例</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>mclmli.operationdiamondv2main.team.TeamFormManager</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTeamFormManager()" class="member-name-link">getTeamFormManager</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取表单管理器实例</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>mclmli.operationdiamondv2main.team.TeamGUIManager</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTeamGUIManager()" class="member-name-link">getTeamGUIManager</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取GUI管理器实例</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="team/TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTeamManager()" class="member-name-link">getTeamManager</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取组队管理器实例</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onDisable()" class="member-name-link">onDisable</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onEnable()" class="member-name-link">onEnable</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.bukkit.plugin.java.JavaPlugin">从类继承的方法&nbsp;org.bukkit.plugin.java.JavaPlugin</h3>
<code>getClassLoader, getCommand, getConfig, getDataFolder, getDefaultBiomeProvider, getDefaultWorldGenerator, getDescription, getFile, getLifecycleManager, getLogger, getPlugin, getPluginLoader, getPluginMeta, getProvidingPlugin, getResource, getServer, getTextResource, init, init, isEnabled, isNaggable, onCommand, onLoad, onTabComplete, reloadConfig, saveConfig, saveDefaultConfig, saveResource, setEnabled, setNaggable, toString</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.bukkit.plugin.PluginBase">从类继承的方法&nbsp;org.bukkit.plugin.PluginBase</h3>
<code>equals, getName, hashCode</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="java.lang中的类或接口" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="java.lang中的类或接口" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.bukkit.plugin.Plugin">从接口继承的方法&nbsp;org.bukkit.plugin.Plugin</h3>
<code>getComponentLogger, getLog4JLogger, getSLF4JLogger</code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>构造器详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>OperationDiamondv2_Main</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">OperationDiamondv2_Main</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="onEnable()">
<h3>onEnable</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">onEnable</span>()</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code>onEnable</code>&nbsp;在接口中&nbsp;<code>org.bukkit.plugin.Plugin</code></dd>
<dt>覆盖:</dt>
<dd><code>onEnable</code>&nbsp;在类中&nbsp;<code>org.bukkit.plugin.java.JavaPlugin</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="onDisable()">
<h3>onDisable</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">onDisable</span>()</div>
<dl class="notes">
<dt>指定者:</dt>
<dd><code>onDisable</code>&nbsp;在接口中&nbsp;<code>org.bukkit.plugin.Plugin</code></dd>
<dt>覆盖:</dt>
<dd><code>onDisable</code>&nbsp;在类中&nbsp;<code>org.bukkit.plugin.java.JavaPlugin</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTeamManager()">
<h3>getTeamManager</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="team/TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a></span>&nbsp;<span class="element-name">getTeamManager</span>()</div>
<div class="block">获取组队管理器实例</div>
<dl class="notes">
<dt>返回:</dt>
<dd>TeamManager实例</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDeviceManager()">
<h3>getDeviceManager</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="device/DeviceManager.html" title="mclmli.operationdiamondv2main.device中的类">DeviceManager</a></span>&nbsp;<span class="element-name">getDeviceManager</span>()</div>
<div class="block">获取设备管理器实例</div>
<dl class="notes">
<dt>返回:</dt>
<dd>DeviceManager实例</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTeamGUIManager()">
<h3>getTeamGUIManager</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">mclmli.operationdiamondv2main.team.TeamGUIManager</span>&nbsp;<span class="element-name">getTeamGUIManager</span>()</div>
<div class="block">获取GUI管理器实例</div>
<dl class="notes">
<dt>返回:</dt>
<dd>TeamGUIManager实例</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTeamFormManager()">
<h3>getTeamFormManager</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">mclmli.operationdiamondv2main.team.TeamFormManager</span>&nbsp;<span class="element-name">getTeamFormManager</span>()</div>
<div class="block">获取表单管理器实例</div>
<dl class="notes">
<dt>返回:</dt>
<dd>TeamFormManager实例</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
