<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Sun Jul 27 00:12:59 CST 2025 -->
<title>DeviceDetector.PlayerDeviceInfo</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-07-27">
<meta name="description" content="declaration: package: mclmli.operationdiamondv2main.device, class: DeviceDetector, class: PlayerDeviceInfo">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="class-use/DeviceDetector.PlayerDeviceInfo.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../help-doc.html#class">帮助</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>概要:</p>
<ul>
<li>嵌套</li>
<li>字段</li>
<li><a href="#constructor-summary">构造器</a></li>
<li><a href="#method-summary">方法</a></li>
</ul>
</li>
<li>
<p>详细资料:</p>
<ul>
<li>字段</li>
<li><a href="#constructor-detail">构造器</a></li>
<li><a href="#method-detail">方法</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">mclmli.operationdiamondv2main.device</a></div>
<h1 title="类 DeviceDetector.PlayerDeviceInfo" class="title">类 DeviceDetector.PlayerDeviceInfo</h1>
</div>
<div class="inheritance" title="继承树"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance">mclmli.operationdiamondv2main.device.DeviceDetector.PlayerDeviceInfo</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>封闭类:</dt>
<dd><code><a href="DeviceDetector.html" title="mclmli.operationdiamondv2main.device中的类">DeviceDetector</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public static class </span><span class="element-name type-name-label">DeviceDetector.PlayerDeviceInfo</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></span></div>
<div class="block">玩家设备信息类 - 简化版</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>构造器概要</h2>
<div class="caption"><span>构造器</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">构造器</div>
<div class="table-header col-last">说明</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.util.UUID,mclmli.operationdiamondv2main.device.DeviceDetector.DeviceType,mclmli.operationdiamondv2main.device.DeviceDetector.InputType,java.lang.String,java.lang.String)" class="member-name-link">PlayerDeviceInfo</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId,
 <a href="DeviceDetector.DeviceType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.DeviceType</a>&nbsp;deviceType,
 <a href="DeviceDetector.InputType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.InputType</a>&nbsp;inputType,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;rawDeviceOs,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;rawInputMode)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDetailedInfo()" class="member-name-link">getDetailedInfo</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取详细信息</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="DeviceDetector.DeviceType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.DeviceType</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDeviceType()" class="member-name-link">getDeviceType</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFullDescription()" class="member-name-link">getFullDescription</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取完整描述</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="DeviceDetector.InputType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.InputType</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getInputType()" class="member-name-link">getInputType</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPlayerId()" class="member-name-link">getPlayerId</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRawDeviceOs()" class="member-name-link">getRawDeviceOs</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRawInputMode()" class="member-name-link">getRawInputMode</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getShortDescription()" class="member-name-link">getShortDescription</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取简短描述</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isBedrockPlayer()" class="member-name-link">isBedrockPlayer</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">是否为基岩版玩家</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isTouchInput()" class="member-name-link">isTouchInput</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">是否使用触屏操作</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="java.lang中的类或接口" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="java.lang中的类或接口" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>构造器详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.util.UUID,mclmli.operationdiamondv2main.device.DeviceDetector.DeviceType,mclmli.operationdiamondv2main.device.DeviceDetector.InputType,java.lang.String,java.lang.String)">
<h3>PlayerDeviceInfo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">PlayerDeviceInfo</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId,
 <a href="DeviceDetector.DeviceType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.DeviceType</a>&nbsp;deviceType,
 <a href="DeviceDetector.InputType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.InputType</a>&nbsp;inputType,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;rawDeviceOs,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;rawInputMode)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="getPlayerId()">
<h3>getPlayerId</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a></span>&nbsp;<span class="element-name">getPlayerId</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getDeviceType()">
<h3>getDeviceType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="DeviceDetector.DeviceType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.DeviceType</a></span>&nbsp;<span class="element-name">getDeviceType</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getInputType()">
<h3>getInputType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="DeviceDetector.InputType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.InputType</a></span>&nbsp;<span class="element-name">getInputType</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getRawDeviceOs()">
<h3>getRawDeviceOs</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">getRawDeviceOs</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getRawInputMode()">
<h3>getRawInputMode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">getRawInputMode</span>()</div>
</section>
</li>
<li>
<section class="detail" id="isBedrockPlayer()">
<h3>isBedrockPlayer</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isBedrockPlayer</span>()</div>
<div class="block">是否为基岩版玩家</div>
</section>
</li>
<li>
<section class="detail" id="isTouchInput()">
<h3>isTouchInput</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isTouchInput</span>()</div>
<div class="block">是否使用触屏操作</div>
</section>
</li>
<li>
<section class="detail" id="getShortDescription()">
<h3>getShortDescription</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">getShortDescription</span>()</div>
<div class="block">获取简短描述</div>
</section>
</li>
<li>
<section class="detail" id="getFullDescription()">
<h3>getFullDescription</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">getFullDescription</span>()</div>
<div class="block">获取完整描述</div>
</section>
</li>
<li>
<section class="detail" id="getDetailedInfo()">
<h3>getDetailedInfo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">getDetailedInfo</span>()</div>
<div class="block">获取详细信息</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
