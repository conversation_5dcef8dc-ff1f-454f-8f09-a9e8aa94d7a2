<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Sun Jul 27 00:12:59 CST 2025 -->
<title>DeviceManager</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-07-27">
<meta name="description" content="declaration: package: mclmli.operationdiamondv2main.device, class: DeviceManager">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="class-use/DeviceManager.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../help-doc.html#class">帮助</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>概要:</p>
<ul>
<li>嵌套</li>
<li>字段</li>
<li><a href="#constructor-summary">构造器</a></li>
<li><a href="#method-summary">方法</a></li>
</ul>
</li>
<li>
<p>详细资料:</p>
<ul>
<li>字段</li>
<li><a href="#constructor-detail">构造器</a></li>
<li><a href="#method-detail">方法</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">mclmli.operationdiamondv2main.device</a></div>
<h1 title="类 DeviceManager" class="title">类 DeviceManager</h1>
</div>
<div class="inheritance" title="继承树"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance">mclmli.operationdiamondv2main.device.DeviceManager</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>所有已实现的接口:</dt>
<dd><code>org.bukkit.event.Listener</code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">DeviceManager</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>
implements org.bukkit.event.Listener</span></div>
<div class="block">设备管理器 - 管理所有玩家的设备信息
 不依赖于特定功能模块，为整个插件提供设备检测服务</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>构造器概要</h2>
<div class="caption"><span>构造器</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">构造器</div>
<div class="table-header col-last">说明</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(mclmli.operationdiamondv2main.OperationDiamondv2_Main,mclmli.operationdiamondv2main.team.TeamManager)" class="member-name-link">DeviceManager</a><wbr>(<a href="../OperationDiamondv2_Main.html" title="mclmli.operationdiamondv2main中的类">OperationDiamondv2_Main</a>&nbsp;plugin,
 <a href="../team/TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a>&nbsp;teamManager)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#clearOfflinePlayerCache()" class="member-name-link">clearOfflinePlayerCache</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">清理离线玩家的设备缓存
 由定时任务调用，清理不在线玩家的缓存数据</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCacheStatistics()" class="member-name-link">getCacheStatistics</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取设备缓存的统计信息</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDevicePrefix(mclmli.operationdiamondv2main.device.DeviceDetector.PlayerDeviceInfo)" class="member-name-link">getDevicePrefix</a><wbr>(<a href="DeviceDetector.PlayerDeviceInfo.html" title="mclmli.operationdiamondv2main.device中的类">DeviceDetector.PlayerDeviceInfo</a>&nbsp;deviceInfo)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取设备类型对应的前缀</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="DeviceDetector.PlayerDeviceInfo.html" title="mclmli.operationdiamondv2main.device中的类">DeviceDetector.PlayerDeviceInfo</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOrDetectPlayerDeviceInfo(org.bukkit.entity.Player)" class="member-name-link">getOrDetectPlayerDeviceInfo</a><wbr>(org.bukkit.entity.Player&nbsp;player)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取在线玩家的设备信息，如果缓存中没有则重新检测</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="DeviceDetector.PlayerDeviceInfo.html" title="mclmli.operationdiamondv2main.device中的类">DeviceDetector.PlayerDeviceInfo</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPlayerDeviceInfo(java.util.UUID)" class="member-name-link">getPlayerDeviceInfo</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取玩家的设备信息</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onPlayerJoin(org.bukkit.event.player.PlayerJoinEvent)" class="member-name-link">onPlayerJoin</a><wbr>(org.bukkit.event.player.PlayerJoinEvent&nbsp;event)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onPlayerQuit(org.bukkit.event.player.PlayerQuitEvent)" class="member-name-link">onPlayerQuit</a><wbr>(org.bukkit.event.player.PlayerQuitEvent&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#refreshPlayerDeviceInfo(org.bukkit.entity.Player)" class="member-name-link">refreshPlayerDeviceInfo</a><wbr>(org.bukkit.entity.Player&nbsp;player)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">手动刷新玩家的设备信息</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="java.lang中的类或接口" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="java.lang中的类或接口" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>构造器详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(mclmli.operationdiamondv2main.OperationDiamondv2_Main,mclmli.operationdiamondv2main.team.TeamManager)">
<h3>DeviceManager</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">DeviceManager</span><wbr><span class="parameters">(<a href="../OperationDiamondv2_Main.html" title="mclmli.operationdiamondv2main中的类">OperationDiamondv2_Main</a>&nbsp;plugin,
 <a href="../team/TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a>&nbsp;teamManager)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="onPlayerJoin(org.bukkit.event.player.PlayerJoinEvent)">
<h3>onPlayerJoin</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">onPlayerJoin</span><wbr><span class="parameters">(org.bukkit.event.player.PlayerJoinEvent&nbsp;event)</span></div>
</section>
</li>
<li>
<section class="detail" id="onPlayerQuit(org.bukkit.event.player.PlayerQuitEvent)">
<h3>onPlayerQuit</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">onPlayerQuit</span><wbr><span class="parameters">(org.bukkit.event.player.PlayerQuitEvent&nbsp;event)</span></div>
</section>
</li>
<li>
<section class="detail" id="getDevicePrefix(mclmli.operationdiamondv2main.device.DeviceDetector.PlayerDeviceInfo)">
<h3>getDevicePrefix</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">getDevicePrefix</span><wbr><span class="parameters">(<a href="DeviceDetector.PlayerDeviceInfo.html" title="mclmli.operationdiamondv2main.device中的类">DeviceDetector.PlayerDeviceInfo</a>&nbsp;deviceInfo)</span></div>
<div class="block">获取设备类型对应的前缀</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>deviceInfo</code> - 设备信息</dd>
<dt>返回:</dt>
<dd>前缀字符串</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="clearOfflinePlayerCache()">
<h3>clearOfflinePlayerCache</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">clearOfflinePlayerCache</span>()</div>
<div class="block">清理离线玩家的设备缓存
 由定时任务调用，清理不在线玩家的缓存数据</div>
</section>
</li>
<li>
<section class="detail" id="getPlayerDeviceInfo(java.util.UUID)">
<h3>getPlayerDeviceInfo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="DeviceDetector.PlayerDeviceInfo.html" title="mclmli.operationdiamondv2main.device中的类">DeviceDetector.PlayerDeviceInfo</a></span>&nbsp;<span class="element-name">getPlayerDeviceInfo</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId)</span></div>
<div class="block">获取玩家的设备信息</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>playerId</code> - 玩家UUID</dd>
<dt>返回:</dt>
<dd>设备信息，如果不存在则返回null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getOrDetectPlayerDeviceInfo(org.bukkit.entity.Player)">
<h3>getOrDetectPlayerDeviceInfo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="DeviceDetector.PlayerDeviceInfo.html" title="mclmli.operationdiamondv2main.device中的类">DeviceDetector.PlayerDeviceInfo</a></span>&nbsp;<span class="element-name">getOrDetectPlayerDeviceInfo</span><wbr><span class="parameters">(org.bukkit.entity.Player&nbsp;player)</span></div>
<div class="block">获取在线玩家的设备信息，如果缓存中没有则重新检测</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>player</code> - 在线玩家</dd>
<dt>返回:</dt>
<dd>设备信息</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCacheStatistics()">
<h3>getCacheStatistics</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a>&gt;</span>&nbsp;<span class="element-name">getCacheStatistics</span>()</div>
<div class="block">获取设备缓存的统计信息</div>
<dl class="notes">
<dt>返回:</dt>
<dd>包含缓存统计信息的Map</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="refreshPlayerDeviceInfo(org.bukkit.entity.Player)">
<h3>refreshPlayerDeviceInfo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">refreshPlayerDeviceInfo</span><wbr><span class="parameters">(org.bukkit.entity.Player&nbsp;player)</span></div>
<div class="block">手动刷新玩家的设备信息</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>player</code> - 要刷新的玩家</dd>
<dt>返回:</dt>
<dd>是否成功刷新</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
