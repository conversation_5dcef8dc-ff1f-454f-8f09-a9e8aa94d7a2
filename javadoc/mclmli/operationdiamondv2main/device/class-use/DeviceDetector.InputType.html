<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Sun Jul 27 00:12:59 CST 2025 -->
<title>枚举类 mclmli.operationdiamondv2main.device.DeviceDetector.InputType的使用</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-07-27">
<meta name="description" content="use: package: mclmli.operationdiamondv2main.device, class: DeviceDetector, enum: InputType">
<meta name="generator" content="javadoc/ClassUseWriter">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-use-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../index.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../DeviceDetector.InputType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">类</a></li>
<li class="nav-bar-cell1-rev">使用</li>
<li><a href="../package-tree.html">树</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html#use">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="枚举类的使用 mclmli.operationdiamondv2main.device.DeviceDetector.InputType" class="title">枚举类的使用<br>mclmli.operationdiamondv2main.device.DeviceDetector.InputType</h1>
</div>
<div class="caption"><span>使用<a href="../DeviceDetector.InputType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.InputType</a>的程序包</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">程序包</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><a href="#mclmli.operationdiamondv2main.device">mclmli.operationdiamondv2main.device</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<section class="class-uses">
<ul class="block-list">
<li>
<section class="detail" id="mclmli.operationdiamondv2main.device">
<h2><a href="../package-summary.html">mclmli.operationdiamondv2main.device</a>中<a href="../DeviceDetector.InputType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.InputType</a>的使用</h2>
<div class="caption"><span>返回<a href="../DeviceDetector.InputType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.InputType</a>的<a href="../package-summary.html">mclmli.operationdiamondv2main.device</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code><a href="../DeviceDetector.InputType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.InputType</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">DeviceDetector.PlayerDeviceInfo.</span><code><a href="../DeviceDetector.PlayerDeviceInfo.html#getInputType()" class="member-name-link">getInputType</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static <a href="../DeviceDetector.InputType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.InputType</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">DeviceDetector.InputType.</span><code><a href="../DeviceDetector.InputType.html#valueOf(java.lang.String)" class="member-name-link">valueOf</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color">
<div class="block">返回带有指定名称的该类的枚举常量。</div>
</div>
<div class="col-first even-row-color"><code>static <a href="../DeviceDetector.InputType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.InputType</a>[]</code></div>
<div class="col-second even-row-color"><span class="type-name-label">DeviceDetector.InputType.</span><code><a href="../DeviceDetector.InputType.html#values()" class="member-name-link">values</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">返回包含该枚举类的常量的数组，
顺序与声明这些常量的顺序相同</div>
</div>
</div>
<div class="caption"><span>参数类型为<a href="../DeviceDetector.InputType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.InputType</a>的<a href="../package-summary.html">mclmli.operationdiamondv2main.device</a>中的构造器</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">限定符</div>
<div class="table-header col-second">构造器</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code>&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="../DeviceDetector.PlayerDeviceInfo.html#%3Cinit%3E(java.util.UUID,mclmli.operationdiamondv2main.device.DeviceDetector.DeviceType,mclmli.operationdiamondv2main.device.DeviceDetector.InputType,java.lang.String,java.lang.String)" class="member-name-link">PlayerDeviceInfo</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId,
 <a href="../DeviceDetector.DeviceType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.DeviceType</a>&nbsp;deviceType,
 <a href="../DeviceDetector.InputType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.InputType</a>&nbsp;inputType,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;rawDeviceOs,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;rawInputMode)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
