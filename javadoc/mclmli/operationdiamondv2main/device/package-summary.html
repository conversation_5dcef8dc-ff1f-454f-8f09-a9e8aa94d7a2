<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Sun Jul 27 00:12:59 CST 2025 -->
<title>mclmli.operationdiamondv2main.device</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-07-27">
<meta name="description" content="declaration: package: mclmli.operationdiamondv2main.device">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../index.html">概览</a></li>
<li class="nav-bar-cell1-rev">程序包</li>
<li>类</li>
<li><a href="package-use.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../help-doc.html#package">帮助</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>程序包：</p>
<ul>
<li>说明</li>
<li><a href="#related-package-summary">相关程序包</a></li>
<li><a href="#class-summary">类和接口</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>程序包：&nbsp;</li>
<li>说明&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">相关程序包</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">类和接口</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="程序包 mclmli.operationdiamondv2main.device" class="title">程序包 mclmli.operationdiamondv2main.device</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">mclmli.operationdiamondv2main.device</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>相关程序包</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">程序包</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><a href="../package-summary.html">mclmli.operationdiamondv2main</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="../team/package-summary.html">mclmli.operationdiamondv2main.team</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">所有类和接口</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">类</button><button id="class-summary-tab3" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab3', 2)" class="table-tab">枚举类</button></div>
<div id="class-summary.tabpanel" role="tabpanel" aria-labelledby="class-summary-tab0">
<div class="summary-table two-column-summary">
<div class="table-header col-first">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="DeviceDetector.html" title="mclmli.operationdiamondv2main.device中的类">DeviceDetector</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">简化设备检测器 - 只检测基岩版/Java版和触屏/非触屏</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab3"><a href="DeviceDetector.DeviceType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.DeviceType</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab3">
<div class="block">设备类型枚举 - 简化版</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab3"><a href="DeviceDetector.InputType.html" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.InputType</a></div>
<div class="col-last even-row-color class-summary class-summary-tab3">
<div class="block">输入方式枚举 - 简化版</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="DeviceDetector.PlayerDeviceInfo.html" title="mclmli.operationdiamondv2main.device中的类">DeviceDetector.PlayerDeviceInfo</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">玩家设备信息类 - 简化版</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="DeviceManager.html" title="mclmli.operationdiamondv2main.device中的类">DeviceManager</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">设备管理器 - 管理所有玩家的设备信息
 不依赖于特定功能模块，为整个插件提供设备检测服务</div>
</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
