<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Sun Jul 27 00:12:59 CST 2025 -->
<title>程序包 mclmli.operationdiamondv2main的使用</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-07-27">
<meta name="description" content="use: package: mclmli.operationdiamondv2main">
<meta name="generator" content="javadoc/PackageUseWriter">
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
<script type="text/javascript" src="../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-use-page">
<script type="text/javascript">var pathtoroot = "../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li class="nav-bar-cell1-rev">使用</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../index-files/index-1.html">索引</a></li>
<li><a href="../../help-doc.html#use">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="程序包的使用 mclmli.operationdiamondv2main" class="title">程序包的使用<br>mclmli.operationdiamondv2main</h1>
</div>
<div class="caption"><span>使用<a href="package-summary.html">mclmli.operationdiamondv2main</a>的程序包</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">程序包</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><a href="#mclmli.operationdiamondv2main.device">mclmli.operationdiamondv2main.device</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<section class="package-uses">
<ul class="block-list">
<li>
<section class="detail" id="mclmli.operationdiamondv2main.device">
<div class="caption"><span><a href="device/package-summary.html">mclmli.operationdiamondv2main.device</a>使用的<a href="package-summary.html">mclmli.operationdiamondv2main</a>中的类</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><a href="class-use/OperationDiamondv2_Main.html#mclmli.operationdiamondv2main.device">OperationDiamondv2_Main</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
