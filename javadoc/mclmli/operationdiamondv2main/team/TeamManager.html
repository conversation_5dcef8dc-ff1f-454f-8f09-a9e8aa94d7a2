<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Sun Jul 27 00:12:59 CST 2025 -->
<title>TeamManager</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-07-27">
<meta name="description" content="declaration: package: mclmli.operationdiamondv2main.team, class: TeamManager">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="class-use/TeamManager.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../help-doc.html#class">帮助</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>概要:</p>
<ul>
<li><a href="#nested-class-summary">嵌套</a></li>
<li>字段</li>
<li><a href="#constructor-summary">构造器</a></li>
<li><a href="#method-summary">方法</a></li>
</ul>
</li>
<li>
<p>详细资料:</p>
<ul>
<li>字段</li>
<li><a href="#constructor-detail">构造器</a></li>
<li><a href="#method-detail">方法</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li><a href="#nested-class-summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">mclmli.operationdiamondv2main.team</a></div>
<h1 title="类 TeamManager" class="title">类 TeamManager</h1>
</div>
<div class="inheritance" title="继承树"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance">mclmli.operationdiamondv2main.team.TeamManager</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">TeamManager</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></span></div>
<div class="block">组队管理器 - 负责处理所有组队相关的逻辑
 支持Java版和基岩版玩家的跨平台组队功能
 集成Geyser和Floodgate API提供完整的跨平台支持</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>嵌套类概要</h2>
<div class="caption"><span>嵌套类</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code>static enum&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="TeamManager.JoinResult.html" class="type-name-link" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamManager.JoinResult</a></code></div>
<div class="col-last even-row-color">
<div class="block">加入队伍的结果枚举</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>构造器概要</h2>
<div class="caption"><span>构造器</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">构造器</div>
<div class="table-header col-last">说明</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">TeamManager</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#canSendForm(java.util.UUID)" class="member-name-link">canSendForm</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">检查是否支持发送表单给玩家</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#cleanupExpired()" class="member-name-link">cleanupExpired</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">定期清理过期的邀请和申请</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="TeamApplication.html" title="mclmli.operationdiamondv2main.team中的类">TeamApplication</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createApplication(java.lang.String,java.util.UUID,java.lang.String)" class="member-name-link">createApplication</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;teamId,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;applicantId,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;message)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">创建申请</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="TeamInvitation.html" title="mclmli.operationdiamondv2main.team中的类">TeamInvitation</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createInvitation(java.lang.String,java.util.UUID,java.util.UUID)" class="member-name-link">createInvitation</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;teamId,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;inviterId,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;inviteeId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">创建邀请</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createTeam(java.lang.String,java.util.UUID)" class="member-name-link">createTeam</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;teamName,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;leaderId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">创建队伍</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#disbandTeam(java.lang.String)" class="member-name-link">disbandTeam</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;teamId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">解散队伍</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#findTeam(java.lang.String)" class="member-name-link">findTeam</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;identifier)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">根据ID或名称查找队伍</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a><wbr>&lt;<a href="Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAllTeams()" class="member-name-link">getAllTeams</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取所有队伍列表</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.geysermc.geyser.api.connection.GeyserConnection</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBedrockConnection(java.util.UUID)" class="member-name-link">getBedrockConnection</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取基岩版玩家的连接信息</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.geysermc.floodgate.api.player.FloodgatePlayer</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFloodgatePlayer(java.util.UUID)" class="member-name-link">getFloodgatePlayer</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取基岩版玩家的Floodgate信息</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOnlineBedrockPlayerCount()" class="member-name-link">getOnlineBedrockPlayerCount</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取在线基岩版玩家数量</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPlayerFullDeviceInfo(java.util.UUID)" class="member-name-link">getPlayerFullDeviceInfo</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取玩家完整设备信息</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="TeamInvitation.html" title="mclmli.operationdiamondv2main.team中的类">TeamInvitation</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPlayerInvitation(java.util.UUID)" class="member-name-link">getPlayerInvitation</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取玩家的邀请</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPlayerPlatformInfo(java.util.UUID)" class="member-name-link">getPlayerPlatformInfo</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取基岩版玩家的详细信息</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPlayerTeam(java.util.UUID)" class="member-name-link">getPlayerTeam</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取玩家所在的队伍</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a><wbr>&lt;<a href="Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPublicTeams()" class="member-name-link">getPublicTeams</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取所有公开的队伍列表（用于GUI显示）</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTeam(java.lang.String)" class="member-name-link">getTeam</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;teamId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">根据ID获取队伍</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="TeamApplication.html" title="mclmli.operationdiamondv2main.team中的类">TeamApplication</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTeamApplication(java.lang.String,java.util.UUID)" class="member-name-link">getTeamApplication</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;teamId,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;applicantId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">获取队伍的申请</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#hasFullCrossPlatformSupport()" class="member-name-link">hasFullCrossPlatformSupport</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">检查是否有完整的跨平台支持</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#initialize()" class="member-name-link">initialize</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">初始化组队管理器</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isBedrockPlayer(java.util.UUID)" class="member-name-link">isBedrockPlayer</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">检查玩家是否为基岩版玩家
 优先使用Floodgate API，如果不可用则使用Geyser API</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isBedrockPlayer(org.bukkit.entity.Player)" class="member-name-link">isBedrockPlayer</a><wbr>(org.bukkit.entity.Player&nbsp;player)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">检查玩家是否为基岩版玩家
 优先使用Floodgate API，如果不可用则使用Geyser API</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isFloodgateEnabled()" class="member-name-link">isFloodgateEnabled</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">检查Floodgate是否启用</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isGeyserEnabled()" class="member-name-link">isGeyserEnabled</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">检查Geyser是否启用</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isTeamNameExists(java.lang.String)" class="member-name-link">isTeamNameExists</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;teamName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">检查队伍名称是否已存在</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="TeamManager.JoinResult.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamManager.JoinResult</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#joinTeam(java.util.UUID,java.lang.String,java.lang.String)" class="member-name-link">joinTeam</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;teamId,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;password)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">加入队伍</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="TeamManager.JoinResult.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamManager.JoinResult</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#joinTeam(java.util.UUID,java.lang.String,java.lang.String,mclmli.operationdiamondv2main.device.DeviceManager)" class="member-name-link">joinTeam</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;teamId,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;password,
 <a href="../device/DeviceManager.html" title="mclmli.operationdiamondv2main.device中的类">DeviceManager</a>&nbsp;deviceManager)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">加入队伍（支持设备检查和警告提醒）</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#removeApplication(java.lang.String)" class="member-name-link">removeApplication</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;applicationId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">移除申请</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#removeFromTeam(java.util.UUID)" class="member-name-link">removeFromTeam</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">从队伍中移除玩家</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#removeInvitation(java.lang.String)" class="member-name-link">removeInvitation</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;invitationId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">移除邀请</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#shouldUseFormInterface(java.util.UUID)" class="member-name-link">shouldUseFormInterface</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">检查玩家是否适合使用表单界面</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="java.lang中的类或接口" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="java.lang中的类或接口" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>构造器详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>TeamManager</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">TeamManager</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="initialize()">
<h3>initialize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">initialize</span>()</div>
<div class="block">初始化组队管理器</div>
</section>
</li>
<li>
<section class="detail" id="isBedrockPlayer(org.bukkit.entity.Player)">
<h3>isBedrockPlayer</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isBedrockPlayer</span><wbr><span class="parameters">(org.bukkit.entity.Player&nbsp;player)</span></div>
<div class="block">检查玩家是否为基岩版玩家
 优先使用Floodgate API，如果不可用则使用Geyser API</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>player</code> - 要检查的玩家</dd>
<dt>返回:</dt>
<dd>如果是基岩版玩家返回true，否则返回false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isBedrockPlayer(java.util.UUID)">
<h3>isBedrockPlayer</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isBedrockPlayer</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId)</span></div>
<div class="block">检查玩家是否为基岩版玩家
 优先使用Floodgate API，如果不可用则使用Geyser API</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>playerId</code> - 玩家UUID</dd>
<dt>返回:</dt>
<dd>如果是基岩版玩家返回true，否则返回false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFloodgatePlayer(java.util.UUID)">
<h3>getFloodgatePlayer</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">org.geysermc.floodgate.api.player.FloodgatePlayer</span>&nbsp;<span class="element-name">getFloodgatePlayer</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId)</span></div>
<div class="block">获取基岩版玩家的Floodgate信息</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>playerId</code> - 玩家UUID</dd>
<dt>返回:</dt>
<dd>FloodgatePlayer实例，如果不是基岩版玩家或Floodgate不可用则返回null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getBedrockConnection(java.util.UUID)">
<h3>getBedrockConnection</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">org.geysermc.geyser.api.connection.GeyserConnection</span>&nbsp;<span class="element-name">getBedrockConnection</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId)</span></div>
<div class="block">获取基岩版玩家的连接信息</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>playerId</code> - 玩家UUID</dd>
<dt>返回:</dt>
<dd>基岩版玩家连接，如果不是基岩版玩家则返回null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getOnlineBedrockPlayerCount()">
<h3>getOnlineBedrockPlayerCount</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getOnlineBedrockPlayerCount</span>()</div>
<div class="block">获取在线基岩版玩家数量</div>
<dl class="notes">
<dt>返回:</dt>
<dd>在线基岩版玩家数量</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getPlayerPlatformInfo(java.util.UUID)">
<h3>getPlayerPlatformInfo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">getPlayerPlatformInfo</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId)</span></div>
<div class="block">获取基岩版玩家的详细信息</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>playerId</code> - 玩家UUID</dd>
<dt>返回:</dt>
<dd>包含平台、设备类型等信息的字符串，如果不是基岩版玩家则返回"Java版"</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getPlayerFullDeviceInfo(java.util.UUID)">
<h3>getPlayerFullDeviceInfo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">getPlayerFullDeviceInfo</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId)</span></div>
<div class="block">获取玩家完整设备信息</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>playerId</code> - 玩家UUID</dd>
<dt>返回:</dt>
<dd>完整设备信息字符串</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="shouldUseFormInterface(java.util.UUID)">
<h3>shouldUseFormInterface</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">shouldUseFormInterface</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId)</span></div>
<div class="block">检查玩家是否适合使用表单界面</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>playerId</code> - 玩家UUID</dd>
<dt>返回:</dt>
<dd>是否推荐使用表单界面</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isGeyserEnabled()">
<h3>isGeyserEnabled</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isGeyserEnabled</span>()</div>
<div class="block">检查Geyser是否启用</div>
<dl class="notes">
<dt>返回:</dt>
<dd>如果Geyser API可用返回true</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isFloodgateEnabled()">
<h3>isFloodgateEnabled</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isFloodgateEnabled</span>()</div>
<div class="block">检查Floodgate是否启用</div>
<dl class="notes">
<dt>返回:</dt>
<dd>如果Floodgate API可用返回true</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="hasFullCrossPlatformSupport()">
<h3>hasFullCrossPlatformSupport</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">hasFullCrossPlatformSupport</span>()</div>
<div class="block">检查是否有完整的跨平台支持</div>
<dl class="notes">
<dt>返回:</dt>
<dd>如果Geyser和Floodgate都可用返回true</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createTeam(java.lang.String,java.util.UUID)">
<h3>createTeam</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></span>&nbsp;<span class="element-name">createTeam</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;teamName,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;leaderId)</span></div>
<div class="block">创建队伍</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>teamName</code> - 队伍名称</dd>
<dd><code>leaderId</code> - 队长UUID</dd>
<dt>返回:</dt>
<dd>创建的队伍，失败返回null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isTeamNameExists(java.lang.String)">
<h3>isTeamNameExists</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isTeamNameExists</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;teamName)</span></div>
<div class="block">检查队伍名称是否已存在</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>teamName</code> - 队伍名称</dd>
<dt>返回:</dt>
<dd>是否存在</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getPlayerTeam(java.util.UUID)">
<h3>getPlayerTeam</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></span>&nbsp;<span class="element-name">getPlayerTeam</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId)</span></div>
<div class="block">获取玩家所在的队伍</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>playerId</code> - 玩家UUID</dd>
<dt>返回:</dt>
<dd>队伍对象，不在队伍中返回null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="findTeam(java.lang.String)">
<h3>findTeam</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></span>&nbsp;<span class="element-name">findTeam</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;identifier)</span></div>
<div class="block">根据ID或名称查找队伍</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>identifier</code> - 队伍ID或名称</dd>
<dt>返回:</dt>
<dd>队伍对象，找不到返回null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTeam(java.lang.String)">
<h3>getTeam</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></span>&nbsp;<span class="element-name">getTeam</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;teamId)</span></div>
<div class="block">根据ID获取队伍</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>teamId</code> - 队伍ID</dd>
<dt>返回:</dt>
<dd>队伍对象，找不到返回null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="joinTeam(java.util.UUID,java.lang.String,java.lang.String)">
<h3>joinTeam</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="TeamManager.JoinResult.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamManager.JoinResult</a></span>&nbsp;<span class="element-name">joinTeam</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;teamId,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;password)</span></div>
<div class="block">加入队伍</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>playerId</code> - 玩家UUID</dd>
<dd><code>teamId</code> - 队伍ID</dd>
<dd><code>password</code> - 密码（如果需要）</dd>
<dt>返回:</dt>
<dd>加入结果</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="joinTeam(java.util.UUID,java.lang.String,java.lang.String,mclmli.operationdiamondv2main.device.DeviceManager)">
<h3>joinTeam</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="TeamManager.JoinResult.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamManager.JoinResult</a></span>&nbsp;<span class="element-name">joinTeam</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;teamId,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;password,
 <a href="../device/DeviceManager.html" title="mclmli.operationdiamondv2main.device中的类">DeviceManager</a>&nbsp;deviceManager)</span></div>
<div class="block">加入队伍（支持设备检查和警告提醒）</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>playerId</code> - 玩家UUID</dd>
<dd><code>teamId</code> - 队伍ID</dd>
<dd><code>password</code> - 密码（如果需要）</dd>
<dd><code>deviceManager</code> - 设备管理器</dd>
<dt>返回:</dt>
<dd>加入结果</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAllTeams()">
<h3>getAllTeams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a>&gt;</span>&nbsp;<span class="element-name">getAllTeams</span>()</div>
<div class="block">获取所有队伍列表</div>
<dl class="notes">
<dt>返回:</dt>
<dd>所有队伍的列表</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getPublicTeams()">
<h3>getPublicTeams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="java.util中的类或接口" class="external-link">List</a>&lt;<a href="Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a>&gt;</span>&nbsp;<span class="element-name">getPublicTeams</span>()</div>
<div class="block">获取所有公开的队伍列表（用于GUI显示）</div>
<dl class="notes">
<dt>返回:</dt>
<dd>公开队伍的列表</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="disbandTeam(java.lang.String)">
<h3>disbandTeam</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">disbandTeam</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;teamId)</span></div>
<div class="block">解散队伍</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>teamId</code> - 队伍ID</dd>
<dt>返回:</dt>
<dd>是否成功解散</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="removeFromTeam(java.util.UUID)">
<h3>removeFromTeam</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">removeFromTeam</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId)</span></div>
<div class="block">从队伍中移除玩家</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>playerId</code> - 玩家UUID</dd>
<dt>返回:</dt>
<dd>是否成功移除</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createInvitation(java.lang.String,java.util.UUID,java.util.UUID)">
<h3>createInvitation</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="TeamInvitation.html" title="mclmli.operationdiamondv2main.team中的类">TeamInvitation</a></span>&nbsp;<span class="element-name">createInvitation</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;teamId,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;inviterId,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;inviteeId)</span></div>
<div class="block">创建邀请</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>teamId</code> - 队伍ID</dd>
<dd><code>inviterId</code> - 邀请者UUID</dd>
<dd><code>inviteeId</code> - 被邀请者UUID</dd>
<dt>返回:</dt>
<dd>邀请对象，失败返回null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getPlayerInvitation(java.util.UUID)">
<h3>getPlayerInvitation</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="TeamInvitation.html" title="mclmli.operationdiamondv2main.team中的类">TeamInvitation</a></span>&nbsp;<span class="element-name">getPlayerInvitation</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId)</span></div>
<div class="block">获取玩家的邀请</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>playerId</code> - 玩家UUID</dd>
<dt>返回:</dt>
<dd>邀请对象，没有邀请返回null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="removeInvitation(java.lang.String)">
<h3>removeInvitation</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">removeInvitation</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;invitationId)</span></div>
<div class="block">移除邀请</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>invitationId</code> - 邀请ID</dd>
<dt>返回:</dt>
<dd>是否成功移除</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createApplication(java.lang.String,java.util.UUID,java.lang.String)">
<h3>createApplication</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="TeamApplication.html" title="mclmli.operationdiamondv2main.team中的类">TeamApplication</a></span>&nbsp;<span class="element-name">createApplication</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;teamId,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;applicantId,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;message)</span></div>
<div class="block">创建申请</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>teamId</code> - 队伍ID</dd>
<dd><code>applicantId</code> - 申请者UUID</dd>
<dd><code>message</code> - 申请留言</dd>
<dt>返回:</dt>
<dd>申请对象，失败返回null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTeamApplication(java.lang.String,java.util.UUID)">
<h3>getTeamApplication</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="TeamApplication.html" title="mclmli.operationdiamondv2main.team中的类">TeamApplication</a></span>&nbsp;<span class="element-name">getTeamApplication</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;teamId,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;applicantId)</span></div>
<div class="block">获取队伍的申请</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>teamId</code> - 队伍ID</dd>
<dd><code>applicantId</code> - 申请者UUID</dd>
<dt>返回:</dt>
<dd>申请对象，没有申请返回null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="removeApplication(java.lang.String)">
<h3>removeApplication</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">removeApplication</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;applicationId)</span></div>
<div class="block">移除申请</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>applicationId</code> - 申请ID</dd>
<dt>返回:</dt>
<dd>是否成功移除</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="canSendForm(java.util.UUID)">
<h3>canSendForm</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">canSendForm</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId)</span></div>
<div class="block">检查是否支持发送表单给玩家</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>playerId</code> - 玩家UUID</dd>
<dt>返回:</dt>
<dd>如果可以发送表单返回true</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="cleanupExpired()">
<h3>cleanupExpired</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">cleanupExpired</span>()</div>
<div class="block">定期清理过期的邀请和申请</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
