<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Sun Jul 27 00:12:59 CST 2025 -->
<title>类 mclmli.operationdiamondv2main.team.TeamManager的使用</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-07-27">
<meta name="description" content="use: package: mclmli.operationdiamondv2main.team, class: TeamManager">
<meta name="generator" content="javadoc/ClassUseWriter">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-use-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../index.html">概览</a></li>
<li><a href="../package-summary.html">程序包</a></li>
<li><a href="../TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">类</a></li>
<li class="nav-bar-cell1-rev">使用</li>
<li><a href="../package-tree.html">树</a></li>
<li><a href="../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../help-doc.html#use">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="类的使用 mclmli.operationdiamondv2main.team.TeamManager" class="title">类的使用<br>mclmli.operationdiamondv2main.team.TeamManager</h1>
</div>
<div class="caption"><span>使用<a href="../TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a>的程序包</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">程序包</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><a href="#mclmli.operationdiamondv2main">mclmli.operationdiamondv2main</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#mclmli.operationdiamondv2main.device">mclmli.operationdiamondv2main.device</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="#mclmli.operationdiamondv2main.team">mclmli.operationdiamondv2main.team</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<section class="class-uses">
<ul class="block-list">
<li>
<section class="detail" id="mclmli.operationdiamondv2main">
<h2><a href="../../package-summary.html">mclmli.operationdiamondv2main</a>中<a href="../TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a>的使用</h2>
<div class="caption"><span>返回<a href="../TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a>的<a href="../../package-summary.html">mclmli.operationdiamondv2main</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code><a href="../TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">OperationDiamondv2_Main.</span><code><a href="../../OperationDiamondv2_Main.html#getTeamManager()" class="member-name-link">getTeamManager</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">获取组队管理器实例</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="mclmli.operationdiamondv2main.device">
<h2><a href="../../device/package-summary.html">mclmli.operationdiamondv2main.device</a>中<a href="../TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a>的使用</h2>
<div class="caption"><span>参数类型为<a href="../TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a>的<a href="../../device/package-summary.html">mclmli.operationdiamondv2main.device</a>中的方法</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code>static <a href="../../device/DeviceDetector.PlayerDeviceInfo.html" title="mclmli.operationdiamondv2main.device中的类">DeviceDetector.PlayerDeviceInfo</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">DeviceDetector.</span><code><a href="../../device/DeviceDetector.html#detectPlayerDevice(java.util.UUID,mclmli.operationdiamondv2main.team.TeamManager)" class="member-name-link">detectPlayerDevice</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html" title="java.util中的类或接口" class="external-link">UUID</a>&nbsp;playerId,
 <a href="../TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a>&nbsp;teamManager)</code></div>
<div class="col-last even-row-color">
<div class="block">检测玩家设备信息</div>
</div>
</div>
<div class="caption"><span>参数类型为<a href="../TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a>的<a href="../../device/package-summary.html">mclmli.operationdiamondv2main.device</a>中的构造器</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">限定符</div>
<div class="table-header col-second">构造器</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code>&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="../../device/DeviceManager.html#%3Cinit%3E(mclmli.operationdiamondv2main.OperationDiamondv2_Main,mclmli.operationdiamondv2main.team.TeamManager)" class="member-name-link">DeviceManager</a><wbr>(<a href="../../OperationDiamondv2_Main.html" title="mclmli.operationdiamondv2main中的类">OperationDiamondv2_Main</a>&nbsp;plugin,
 <a href="../TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a>&nbsp;teamManager)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<li>
<section class="detail" id="mclmli.operationdiamondv2main.team">
<h2><a href="../package-summary.html">mclmli.operationdiamondv2main.team</a>中<a href="../TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a>的使用</h2>
<div class="caption"><span>参数类型为<a href="../TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a>的<a href="../package-summary.html">mclmli.operationdiamondv2main.team</a>中的构造器</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">限定符</div>
<div class="table-header col-second">构造器</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code>&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="../TeamCommand.html#%3Cinit%3E(mclmli.operationdiamondv2main.team.TeamManager,mclmli.operationdiamondv2main.device.DeviceManager,mclmli.operationdiamondv2main.team.TeamGUIManager,mclmli.operationdiamondv2main.team.TeamFormManager)" class="member-name-link">TeamCommand</a><wbr>(<a href="../TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a>&nbsp;teamManager,
 <a href="../../device/DeviceManager.html" title="mclmli.operationdiamondv2main.device中的类">DeviceManager</a>&nbsp;deviceManager,
 mclmli.operationdiamondv2main.team.TeamGUIManager&nbsp;teamGUIManager,
 mclmli.operationdiamondv2main.team.TeamFormManager&nbsp;teamFormManager)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="../TeamDeviceAnalyzer.html#%3Cinit%3E(mclmli.operationdiamondv2main.team.TeamManager)" class="member-name-link">TeamDeviceAnalyzer</a><wbr>(<a href="../TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a>&nbsp;teamManager)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="../TeamJoinValidator.html#%3Cinit%3E(mclmli.operationdiamondv2main.team.TeamManager)" class="member-name-link">TeamJoinValidator</a><wbr>(<a href="../TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a>&nbsp;teamManager)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
