<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Sun Jul 27 00:12:59 CST 2025 -->
<title>mclmli.operationdiamondv2main.team</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-07-27">
<meta name="description" content="declaration: package: mclmli.operationdiamondv2main.team">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../index.html">概览</a></li>
<li class="nav-bar-cell1-rev">程序包</li>
<li>类</li>
<li><a href="package-use.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../help-doc.html#package">帮助</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>程序包：</p>
<ul>
<li>说明</li>
<li><a href="#related-package-summary">相关程序包</a></li>
<li><a href="#class-summary">类和接口</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>程序包：&nbsp;</li>
<li>说明&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">相关程序包</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">类和接口</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="程序包 mclmli.operationdiamondv2main.team" class="title">程序包 mclmli.operationdiamondv2main.team</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">mclmli.operationdiamondv2main.team</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>相关程序包</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">程序包</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><a href="../package-summary.html">mclmli.operationdiamondv2main</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="../device/package-summary.html">mclmli.operationdiamondv2main.device</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">所有类和接口</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">类</button><button id="class-summary-tab3" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab3', 2)" class="table-tab">枚举类</button></div>
<div id="class-summary.tabpanel" role="tabpanel" aria-labelledby="class-summary-tab0">
<div class="summary-table two-column-summary">
<div class="table-header col-first">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Team.html" title="mclmli.operationdiamondv2main.team中的类">Team</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">队伍数据类
 包含队伍的所有信息和成员管理</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TeamApplication.html" title="mclmli.operationdiamondv2main.team中的类">TeamApplication</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">队伍申请数据类
 处理队伍申请相关信息</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab3"><a href="TeamApplication.ApplicationStatus.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamApplication.ApplicationStatus</a></div>
<div class="col-last even-row-color class-summary class-summary-tab3">
<div class="block">申请状态枚举</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TeamCommand.html" title="mclmli.operationdiamondv2main.team中的类">TeamCommand</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">组队指令处理器
 处理所有组队相关的指令</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TeamDeviceAnalyzer.html" title="mclmli.operationdiamondv2main.team中的类">TeamDeviceAnalyzer</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">队伍设备组成分析器
 用于分析队伍中玩家的设备类型组成，判断队伍类型</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TeamDeviceAnalyzer.DeviceComposition.html" title="mclmli.operationdiamondv2main.team中的类">TeamDeviceAnalyzer.DeviceComposition</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">设备组成分析结果</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab3"><a href="TeamDeviceRestriction.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamDeviceRestriction</a></div>
<div class="col-last even-row-color class-summary class-summary-tab3">
<div class="block">队伍设备限制枚举
 定义队伍允许加入的设备类型限制</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab3"><a href="TeamDeviceType.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamDeviceType</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab3">
<div class="block">队伍设备类型枚举
 基于队伍成员的设备组成来分类队伍类型</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TeamInvitation.html" title="mclmli.operationdiamondv2main.team中的类">TeamInvitation</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">队伍邀请数据类
 处理队伍邀请相关信息</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TeamJoinValidator.html" title="mclmli.operationdiamondv2main.team中的类">TeamJoinValidator</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">队伍加入验证和警告系统
 处理玩家加入队伍时的设备类型验证和警告消息</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="TeamJoinValidator.JoinValidationResult.html" title="mclmli.operationdiamondv2main.team中的类">TeamJoinValidator.JoinValidationResult</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">加入验证结果类</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="TeamManager.html" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">组队管理器 - 负责处理所有组队相关的逻辑
 支持Java版和基岩版玩家的跨平台组队功能
 集成Geyser和Floodgate API提供完整的跨平台支持</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab3"><a href="TeamManager.JoinResult.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamManager.JoinResult</a></div>
<div class="col-last even-row-color class-summary class-summary-tab3">
<div class="block">加入队伍的结果枚举</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab3"><a href="TeamStatus.html" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamStatus</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab3">
<div class="block">队伍状态枚举
 定义队伍的不同访问和加入模式</div>
</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
