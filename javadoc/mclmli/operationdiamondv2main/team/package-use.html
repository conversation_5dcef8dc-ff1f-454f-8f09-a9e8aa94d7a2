<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Sun Jul 27 00:12:59 CST 2025 -->
<title>程序包 mclmli.operationdiamondv2main.team的使用</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-07-27">
<meta name="description" content="use: package: mclmli.operationdiamondv2main.team">
<meta name="generator" content="javadoc/PackageUseWriter">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-use-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li>类</li>
<li class="nav-bar-cell1-rev">使用</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../help-doc.html#use">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="程序包的使用 mclmli.operationdiamondv2main.team" class="title">程序包的使用<br>mclmli.operationdiamondv2main.team</h1>
</div>
<div class="caption"><span>使用<a href="package-summary.html">mclmli.operationdiamondv2main.team</a>的程序包</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">程序包</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><a href="#mclmli.operationdiamondv2main">mclmli.operationdiamondv2main</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#mclmli.operationdiamondv2main.device">mclmli.operationdiamondv2main.device</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="#mclmli.operationdiamondv2main.team">mclmli.operationdiamondv2main.team</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<section class="package-uses">
<ul class="block-list">
<li>
<section class="detail" id="mclmli.operationdiamondv2main">
<div class="caption"><span><a href="../package-summary.html">mclmli.operationdiamondv2main</a>使用的<a href="package-summary.html">mclmli.operationdiamondv2main.team</a>中的类</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><a href="class-use/TeamFormManager.html#mclmli.operationdiamondv2main">TeamFormManager</a></div>
<div class="col-last even-row-color">
<div class="block">队伍表单管理器 - 为基岩版玩家提供表单UI界面
 使用Geyser/Floodgate的Cumulus API</div>
</div>
<div class="col-first odd-row-color"><a href="class-use/TeamGUIManager.html#mclmli.operationdiamondv2main">TeamGUIManager</a></div>
<div class="col-last odd-row-color">
<div class="block">队伍GUI管理器 - 为Java版玩家提供图形化界面</div>
</div>
<div class="col-first even-row-color"><a href="class-use/TeamManager.html#mclmli.operationdiamondv2main">TeamManager</a></div>
<div class="col-last even-row-color">
<div class="block">组队管理器 - 负责处理所有组队相关的逻辑
 支持Java版和基岩版玩家的跨平台组队功能
 集成Geyser和Floodgate API提供完整的跨平台支持</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="mclmli.operationdiamondv2main.device">
<div class="caption"><span><a href="../device/package-summary.html">mclmli.operationdiamondv2main.device</a>使用的<a href="package-summary.html">mclmli.operationdiamondv2main.team</a>中的类</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><a href="class-use/TeamManager.html#mclmli.operationdiamondv2main.device">TeamManager</a></div>
<div class="col-last even-row-color">
<div class="block">组队管理器 - 负责处理所有组队相关的逻辑
 支持Java版和基岩版玩家的跨平台组队功能
 集成Geyser和Floodgate API提供完整的跨平台支持</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="mclmli.operationdiamondv2main.team">
<div class="caption"><span><a href="package-summary.html">mclmli.operationdiamondv2main.team</a>使用的<a href="package-summary.html">mclmli.operationdiamondv2main.team</a>中的类</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><a href="class-use/Team.html#mclmli.operationdiamondv2main.team">Team</a></div>
<div class="col-last even-row-color">
<div class="block">队伍数据类
 包含队伍的所有信息和成员管理</div>
</div>
<div class="col-first odd-row-color"><a href="class-use/TeamApplication.html#mclmli.operationdiamondv2main.team">TeamApplication</a></div>
<div class="col-last odd-row-color">
<div class="block">队伍申请数据类
 处理队伍申请相关信息</div>
</div>
<div class="col-first even-row-color"><a href="class-use/TeamApplication.ApplicationStatus.html#mclmli.operationdiamondv2main.team">TeamApplication.ApplicationStatus</a></div>
<div class="col-last even-row-color">
<div class="block">申请状态枚举</div>
</div>
<div class="col-first odd-row-color"><a href="class-use/TeamDeviceAnalyzer.DeviceComposition.html#mclmli.operationdiamondv2main.team">TeamDeviceAnalyzer.DeviceComposition</a></div>
<div class="col-last odd-row-color">
<div class="block">设备组成分析结果</div>
</div>
<div class="col-first even-row-color"><a href="class-use/TeamDeviceRestriction.html#mclmli.operationdiamondv2main.team">TeamDeviceRestriction</a></div>
<div class="col-last even-row-color">
<div class="block">队伍设备限制枚举
 定义队伍允许加入的设备类型限制</div>
</div>
<div class="col-first odd-row-color"><a href="class-use/TeamDeviceType.html#mclmli.operationdiamondv2main.team">TeamDeviceType</a></div>
<div class="col-last odd-row-color">
<div class="block">队伍设备类型枚举
 基于队伍成员的设备组成来分类队伍类型</div>
</div>
<div class="col-first even-row-color"><a href="class-use/TeamFormManager.html#mclmli.operationdiamondv2main.team">TeamFormManager</a></div>
<div class="col-last even-row-color">
<div class="block">队伍表单管理器 - 为基岩版玩家提供表单UI界面
 使用Geyser/Floodgate的Cumulus API</div>
</div>
<div class="col-first odd-row-color"><a href="class-use/TeamGUIManager.html#mclmli.operationdiamondv2main.team">TeamGUIManager</a></div>
<div class="col-last odd-row-color">
<div class="block">队伍GUI管理器 - 为Java版玩家提供图形化界面</div>
</div>
<div class="col-first even-row-color"><a href="class-use/TeamInvitation.html#mclmli.operationdiamondv2main.team">TeamInvitation</a></div>
<div class="col-last even-row-color">
<div class="block">队伍邀请数据类
 处理队伍邀请相关信息</div>
</div>
<div class="col-first odd-row-color"><a href="class-use/TeamJoinValidator.JoinValidationResult.html#mclmli.operationdiamondv2main.team">TeamJoinValidator.JoinValidationResult</a></div>
<div class="col-last odd-row-color">
<div class="block">加入验证结果类</div>
</div>
<div class="col-first even-row-color"><a href="class-use/TeamManager.html#mclmli.operationdiamondv2main.team">TeamManager</a></div>
<div class="col-last even-row-color">
<div class="block">组队管理器 - 负责处理所有组队相关的逻辑
 支持Java版和基岩版玩家的跨平台组队功能
 集成Geyser和Floodgate API提供完整的跨平台支持</div>
</div>
<div class="col-first odd-row-color"><a href="class-use/TeamManager.JoinResult.html#mclmli.operationdiamondv2main.team">TeamManager.JoinResult</a></div>
<div class="col-last odd-row-color">
<div class="block">加入队伍的结果枚举</div>
</div>
<div class="col-first even-row-color"><a href="class-use/TeamStatus.html#mclmli.operationdiamondv2main.team">TeamStatus</a></div>
<div class="col-last even-row-color">
<div class="block">队伍状态枚举
 定义队伍的不同访问和加入模式</div>
</div>
</div>
</section>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
