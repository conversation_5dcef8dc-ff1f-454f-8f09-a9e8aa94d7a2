memberSearchIndex = [{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"addMember(UUID)","u":"addMember(java.util.UUID)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceRestriction","l":"allowsWarnings()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager.JoinResult","l":"ALREADY_MEMBER"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceAnalyzer","l":"analyzeTeam(Team)","u":"analyzeTeam(mclmli.operationdiamondv2main.team.Team)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager.JoinResult","l":"APPLY_REQUIRED"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamStatus","l":"APPLY_REQUIRED"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamApplication","l":"approve()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamApplication.ApplicationStatus","l":"APPROVED"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceDetector.DeviceType","l":"BEDROCK"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"canSendForm(UUID)","u":"canSendForm(java.util.UUID)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"cleanupExpired()"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceManager","l":"clearOfflinePlayerCache()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"createApplication(String, UUID, String)","u":"createApplication(java.lang.String,java.util.UUID,java.lang.String)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"createInvitation(String, UUID, UUID)","u":"createInvitation(java.lang.String,java.util.UUID,java.util.UUID)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"createTeam(String, UUID)","u":"createTeam(java.lang.String,java.util.UUID)"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceDetector","l":"detectPlayerDevice(UUID, TeamManager)","u":"detectPlayerDevice(java.util.UUID,mclmli.operationdiamondv2main.team.TeamManager)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager.JoinResult","l":"DEVICE_NOT_ALLOWED"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceAnalyzer.DeviceComposition","l":"DeviceComposition(TeamDeviceType, int, int, List<DeviceDetector.PlayerDeviceInfo>)","u":"%3Cinit%3E(mclmli.operationdiamondv2main.team.TeamDeviceType,int,int,java.util.List)"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceDetector","l":"DeviceDetector()","u":"%3Cinit%3E()"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceManager","l":"DeviceManager(OperationDiamondv2_Main, TeamManager)","u":"%3Cinit%3E(mclmli.operationdiamondv2main.OperationDiamondv2_Main,mclmli.operationdiamondv2main.team.TeamManager)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"disbandTeam(String)","u":"disbandTeam(java.lang.String)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamApplication.ApplicationStatus","l":"EXPIRED"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"findTeam(String)","u":"findTeam(java.lang.String)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"getAllTeams()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamApplication","l":"getApplicantId()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamApplication","l":"getApplicationId()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamApplication","l":"getApplicationTime()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"getBedrockConnection(UUID)","u":"getBedrockConnection(java.util.UUID)"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceManager","l":"getCacheStatistics()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceAnalyzer.DeviceComposition","l":"getCompositionDescription()"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"getCreateTime()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceRestriction","l":"getDefault()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceRestriction","l":"getDescription()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceType","l":"getDescription()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamStatus","l":"getDescription()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceAnalyzer.DeviceComposition","l":"getDetailedDeviceList()"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceDetector.PlayerDeviceInfo","l":"getDetailedInfo()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceAnalyzer.DeviceComposition","l":"getDeviceInfos()"},{"p":"mclmli.operationdiamondv2main","c":"OperationDiamondv2_Main","l":"getDeviceManager()"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceManager","l":"getDevicePrefix(DeviceDetector.PlayerDeviceInfo)","u":"getDevicePrefix(mclmli.operationdiamondv2main.device.DeviceDetector.PlayerDeviceInfo)"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceDetector","l":"getDeviceRecommendations(DeviceDetector.PlayerDeviceInfo)","u":"getDeviceRecommendations(mclmli.operationdiamondv2main.device.DeviceDetector.PlayerDeviceInfo)"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"getDeviceRestriction()"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceDetector.PlayerDeviceInfo","l":"getDeviceType()"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceDetector.DeviceType","l":"getDisplayName()"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceDetector.InputType","l":"getDisplayName()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamApplication.ApplicationStatus","l":"getDisplayName()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceRestriction","l":"getDisplayName()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceType","l":"getDisplayName()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamStatus","l":"getDisplayName()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceType","l":"getDisplayNameWithEmoji()"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceDetector.DeviceType","l":"getEmoji()"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceDetector.InputType","l":"getEmoji()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceType","l":"getEmoji()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamApplication","l":"getExpireTime()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamInvitation","l":"getExpireTime()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"getFloodgatePlayer(UUID)","u":"getFloodgatePlayer(java.util.UUID)"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceDetector.PlayerDeviceInfo","l":"getFullDescription()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceRestriction","l":"getFullDescription()"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceDetector.PlayerDeviceInfo","l":"getInputType()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamInvitation","l":"getInvitationId()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamInvitation","l":"getInviteeId()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamInvitation","l":"getInviterId()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamInvitation","l":"getInviteTime()"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"getLeaderId()"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"getMaxMembers()"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"getMemberCount()"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"getMembers()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamApplication","l":"getMessage()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamJoinValidator.JoinValidationResult","l":"getMessage()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceAnalyzer.DeviceComposition","l":"getNonTouchscreenCount()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"getOnlineBedrockPlayerCount()"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceManager","l":"getOrDetectPlayerDeviceInfo(Player)","u":"getOrDetectPlayerDeviceInfo(org.bukkit.entity.Player)"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"getPassword()"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceManager","l":"getPlayerDeviceInfo(UUID)","u":"getPlayerDeviceInfo(java.util.UUID)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"getPlayerFullDeviceInfo(UUID)","u":"getPlayerFullDeviceInfo(java.util.UUID)"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceDetector.PlayerDeviceInfo","l":"getPlayerId()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"getPlayerInvitation(UUID)","u":"getPlayerInvitation(java.util.UUID)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"getPlayerPlatformInfo(UUID)","u":"getPlayerPlatformInfo(java.util.UUID)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"getPlayerTeam(UUID)","u":"getPlayerTeam(java.util.UUID)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"getPublicTeams()"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceDetector.PlayerDeviceInfo","l":"getRawDeviceOs()"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceDetector.PlayerDeviceInfo","l":"getRawInputMode()"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceDetector.PlayerDeviceInfo","l":"getShortDescription()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamApplication","l":"getShortDescription()"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"getStatus()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamApplication","l":"getStatus()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"getTeam(String)","u":"getTeam(java.lang.String)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"getTeamApplication(String, UUID)","u":"getTeamApplication(java.lang.String,java.util.UUID)"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"getTeamDeviceType(DeviceManager)","u":"getTeamDeviceType(mclmli.operationdiamondv2main.device.DeviceManager)"},{"p":"mclmli.operationdiamondv2main","c":"OperationDiamondv2_Main","l":"getTeamFormManager()"},{"p":"mclmli.operationdiamondv2main","c":"OperationDiamondv2_Main","l":"getTeamGUIManager()"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"getTeamId()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamApplication","l":"getTeamId()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamInvitation","l":"getTeamId()"},{"p":"mclmli.operationdiamondv2main","c":"OperationDiamondv2_Main","l":"getTeamManager()"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"getTeamName()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceAnalyzer.DeviceComposition","l":"getTeamType()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceAnalyzer.DeviceComposition","l":"getTotalCount()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceAnalyzer.DeviceComposition","l":"getTouchscreenCount()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"hasFullCrossPlatformSupport()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"initialize()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager.JoinResult","l":"INVITE_ONLY"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamStatus","l":"INVITE_ONLY"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamJoinValidator.JoinValidationResult","l":"isAllowed()"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceDetector.PlayerDeviceInfo","l":"isBedrockPlayer()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"isBedrockPlayer(Player)","u":"isBedrockPlayer(org.bukkit.entity.Player)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"isBedrockPlayer(UUID)","u":"isBedrockPlayer(java.util.UUID)"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"isDeviceAllowed(UUID, DeviceManager)","u":"isDeviceAllowed(java.util.UUID,mclmli.operationdiamondv2main.device.DeviceManager)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamApplication","l":"isExpired()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamInvitation","l":"isExpired()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"isFloodgateEnabled()"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"isFull()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"isGeyserEnabled()"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"isLeader(UUID)","u":"isLeader(java.util.UUID)"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"isMember(UUID)","u":"isMember(java.util.UUID)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"isTeamNameExists(String)","u":"isTeamNameExists(java.lang.String)"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceDetector.PlayerDeviceInfo","l":"isTouchInput()"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"isTouchscreenOnlyTeam(DeviceManager)","u":"isTouchscreenOnlyTeam(mclmli.operationdiamondv2main.device.DeviceManager)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceAnalyzer","l":"isTouchscreenPlayer(DeviceDetector.PlayerDeviceInfo)","u":"isTouchscreenPlayer(mclmli.operationdiamondv2main.device.DeviceDetector.PlayerDeviceInfo)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamApplication","l":"isValid()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamInvitation","l":"isValid()"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceDetector.DeviceType","l":"JAVA"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"joinTeam(UUID, String, String)","u":"joinTeam(java.util.UUID,java.lang.String,java.lang.String)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"joinTeam(UUID, String, String, DeviceManager)","u":"joinTeam(java.util.UUID,java.lang.String,java.lang.String,mclmli.operationdiamondv2main.device.DeviceManager)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamJoinValidator.JoinValidationResult","l":"JoinValidationResult(boolean, String)","u":"%3Cinit%3E(boolean,java.lang.String)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamApplication","l":"markExpired()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamInvitation","l":"markExpired()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceRestriction","l":"MIXED"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceType","l":"MIXED_OR_OTHER"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceDetector.InputType","l":"NON_TOUCHSCREEN"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceRestriction","l":"NON_TOUCHSCREEN_ONLY"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamCommand","l":"onCommand(CommandSender, Command, String, String[])","u":"onCommand(org.bukkit.command.CommandSender,org.bukkit.command.Command,java.lang.String,java.lang.String[])"},{"p":"mclmli.operationdiamondv2main","c":"OperationDiamondv2_Main","l":"onDisable()"},{"p":"mclmli.operationdiamondv2main","c":"OperationDiamondv2_Main","l":"onEnable()"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceManager","l":"onPlayerJoin(PlayerJoinEvent)","u":"onPlayerJoin(org.bukkit.event.player.PlayerJoinEvent)"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceManager","l":"onPlayerQuit(PlayerQuitEvent)","u":"onPlayerQuit(org.bukkit.event.player.PlayerQuitEvent)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamCommand","l":"onTabComplete(CommandSender, Command, String, String[])","u":"onTabComplete(org.bukkit.command.CommandSender,org.bukkit.command.Command,java.lang.String,java.lang.String[])"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamStatus","l":"OPEN"},{"p":"mclmli.operationdiamondv2main","c":"OperationDiamondv2_Main","l":"OperationDiamondv2_Main()","u":"%3Cinit%3E()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamStatus","l":"PASSWORD_REQUIRED"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamApplication.ApplicationStatus","l":"PENDING"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceDetector.PlayerDeviceInfo","l":"PlayerDeviceInfo(UUID, DeviceDetector.DeviceType, DeviceDetector.InputType, String, String)","u":"%3Cinit%3E(java.util.UUID,mclmli.operationdiamondv2main.device.DeviceDetector.DeviceType,mclmli.operationdiamondv2main.device.DeviceDetector.InputType,java.lang.String,java.lang.String)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceAnalyzer","l":"predictTeamTypeAfterJoin(Team, UUID)","u":"predictTeamTypeAfterJoin(mclmli.operationdiamondv2main.team.Team,java.util.UUID)"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceManager","l":"refreshPlayerDeviceInfo(Player)","u":"refreshPlayerDeviceInfo(org.bukkit.entity.Player)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamApplication","l":"reject()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamApplication.ApplicationStatus","l":"REJECTED"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"removeApplication(String)","u":"removeApplication(java.lang.String)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"removeFromTeam(UUID)","u":"removeFromTeam(java.util.UUID)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"removeInvitation(String)","u":"removeInvitation(java.lang.String)"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"removeMember(UUID)","u":"removeMember(java.util.UUID)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceRestriction","l":"requiresDeviceValidation()"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"setDeviceRestriction(TeamDeviceRestriction)","u":"setDeviceRestriction(mclmli.operationdiamondv2main.team.TeamDeviceRestriction)"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"setLeaderId(UUID)","u":"setLeaderId(java.util.UUID)"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"setMaxMembers(int)"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"setPassword(String)","u":"setPassword(java.lang.String)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamApplication","l":"setStatus(TeamApplication.ApplicationStatus)","u":"setStatus(mclmli.operationdiamondv2main.team.TeamApplication.ApplicationStatus)"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"setStatus(TeamStatus)","u":"setStatus(mclmli.operationdiamondv2main.team.TeamStatus)"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"setTeamName(String)","u":"setTeamName(java.lang.String)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"shouldUseFormInterface(UUID)","u":"shouldUseFormInterface(java.util.UUID)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager.JoinResult","l":"SUCCESS"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager.JoinResult","l":"TEAM_FULL"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager.JoinResult","l":"TEAM_NOT_FOUND"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"Team(String, String, UUID)","u":"%3Cinit%3E(java.lang.String,java.lang.String,java.util.UUID)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamApplication","l":"TeamApplication(String, UUID, String)","u":"%3Cinit%3E(java.lang.String,java.util.UUID,java.lang.String)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamCommand","l":"TeamCommand(TeamManager, DeviceManager, TeamGUIManager, TeamFormManager)","u":"%3Cinit%3E(mclmli.operationdiamondv2main.team.TeamManager,mclmli.operationdiamondv2main.device.DeviceManager,mclmli.operationdiamondv2main.team.TeamGUIManager,mclmli.operationdiamondv2main.team.TeamFormManager)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceAnalyzer","l":"TeamDeviceAnalyzer(TeamManager)","u":"%3Cinit%3E(mclmli.operationdiamondv2main.team.TeamManager)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamInvitation","l":"TeamInvitation(String, UUID, UUID)","u":"%3Cinit%3E(java.lang.String,java.util.UUID,java.util.UUID)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamJoinValidator","l":"TeamJoinValidator(TeamManager)","u":"%3Cinit%3E(mclmli.operationdiamondv2main.team.TeamManager)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager","l":"TeamManager()","u":"%3Cinit%3E()"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"toString()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamApplication","l":"toString()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamInvitation","l":"toString()"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceDetector.InputType","l":"TOUCHSCREEN"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceRestriction","l":"TOUCHSCREEN_ONLY"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceType","l":"TOUCHSCREEN_ONLY"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"transferLeadership(UUID)","u":"transferLeadership(java.util.UUID)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamJoinValidator","l":"validateAndWarn(Team, UUID)","u":"validateAndWarn(mclmli.operationdiamondv2main.team.Team,java.util.UUID)"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceDetector.DeviceType","l":"valueOf(String)","u":"valueOf(java.lang.String)"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceDetector.InputType","l":"valueOf(String)","u":"valueOf(java.lang.String)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamApplication.ApplicationStatus","l":"valueOf(String)","u":"valueOf(java.lang.String)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceRestriction","l":"valueOf(String)","u":"valueOf(java.lang.String)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceType","l":"valueOf(String)","u":"valueOf(java.lang.String)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager.JoinResult","l":"valueOf(String)","u":"valueOf(java.lang.String)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamStatus","l":"valueOf(String)","u":"valueOf(java.lang.String)"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceDetector.DeviceType","l":"values()"},{"p":"mclmli.operationdiamondv2main.device","c":"DeviceDetector.InputType","l":"values()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamApplication.ApplicationStatus","l":"values()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceRestriction","l":"values()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamDeviceType","l":"values()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager.JoinResult","l":"values()"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamStatus","l":"values()"},{"p":"mclmli.operationdiamondv2main.team","c":"Team","l":"verifyPassword(String)","u":"verifyPassword(java.lang.String)"},{"p":"mclmli.operationdiamondv2main.team","c":"TeamManager.JoinResult","l":"WRONG_PASSWORD"}];updateSearchResults();