<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Sun Jul 27 00:12:59 CST 2025 -->
<title>类分层结构</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-07-27">
<meta name="description" content="class tree">
<meta name="generator" content="javadoc/TreeWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="tree-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li class="nav-bar-cell1-rev">树</li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html#tree">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 class="title">所有程序包的分层结构</h1>
</div>
<span class="package-hierarchy-label">程序包分层结构:</span>
<ul class="horizontal contents-list">
<li><a href="mclmli/operationdiamondv2main/package-tree.html">mclmli.operationdiamondv2main</a>, </li>
<li><a href="mclmli/operationdiamondv2main/device/package-tree.html">mclmli.operationdiamondv2main.device</a>, </li>
<li><a href="mclmli/operationdiamondv2main/team/package-tree.html">mclmli.operationdiamondv2main.team</a></li>
</ul>
<section class="hierarchy">
<h2 title="类分层结构">类分层结构</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="java.lang中的类或接口">Object</a>
<ul>
<li class="circle">mclmli.operationdiamondv2main.device.<a href="mclmli/operationdiamondv2main/device/DeviceDetector.html" class="type-name-link" title="mclmli.operationdiamondv2main.device中的类">DeviceDetector</a></li>
<li class="circle">mclmli.operationdiamondv2main.device.<a href="mclmli/operationdiamondv2main/device/DeviceDetector.PlayerDeviceInfo.html" class="type-name-link" title="mclmli.operationdiamondv2main.device中的类">DeviceDetector.PlayerDeviceInfo</a></li>
<li class="circle">mclmli.operationdiamondv2main.device.<a href="mclmli/operationdiamondv2main/device/DeviceManager.html" class="type-name-link" title="mclmli.operationdiamondv2main.device中的类">DeviceManager</a> (implements org.bukkit.event.Listener)</li>
<li class="circle">org.bukkit.plugin.PluginBase (implements org.bukkit.plugin.Plugin)
<ul>
<li class="circle">org.bukkit.plugin.java.JavaPlugin
<ul>
<li class="circle">mclmli.operationdiamondv2main.<a href="mclmli/operationdiamondv2main/OperationDiamondv2_Main.html" class="type-name-link" title="mclmli.operationdiamondv2main中的类">OperationDiamondv2_Main</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">mclmli.operationdiamondv2main.team.<a href="mclmli/operationdiamondv2main/team/Team.html" class="type-name-link" title="mclmli.operationdiamondv2main.team中的类">Team</a></li>
<li class="circle">mclmli.operationdiamondv2main.team.<a href="mclmli/operationdiamondv2main/team/TeamApplication.html" class="type-name-link" title="mclmli.operationdiamondv2main.team中的类">TeamApplication</a></li>
<li class="circle">mclmli.operationdiamondv2main.team.<a href="mclmli/operationdiamondv2main/team/TeamCommand.html" class="type-name-link" title="mclmli.operationdiamondv2main.team中的类">TeamCommand</a> (implements org.bukkit.command.CommandExecutor, org.bukkit.command.TabCompleter)</li>
<li class="circle">mclmli.operationdiamondv2main.team.<a href="mclmli/operationdiamondv2main/team/TeamDeviceAnalyzer.html" class="type-name-link" title="mclmli.operationdiamondv2main.team中的类">TeamDeviceAnalyzer</a></li>
<li class="circle">mclmli.operationdiamondv2main.team.<a href="mclmli/operationdiamondv2main/team/TeamDeviceAnalyzer.DeviceComposition.html" class="type-name-link" title="mclmli.operationdiamondv2main.team中的类">TeamDeviceAnalyzer.DeviceComposition</a></li>
<li class="circle">mclmli.operationdiamondv2main.team.<a href="mclmli/operationdiamondv2main/team/TeamInvitation.html" class="type-name-link" title="mclmli.operationdiamondv2main.team中的类">TeamInvitation</a></li>
<li class="circle">mclmli.operationdiamondv2main.team.<a href="mclmli/operationdiamondv2main/team/TeamJoinValidator.html" class="type-name-link" title="mclmli.operationdiamondv2main.team中的类">TeamJoinValidator</a></li>
<li class="circle">mclmli.operationdiamondv2main.team.<a href="mclmli/operationdiamondv2main/team/TeamJoinValidator.JoinValidationResult.html" class="type-name-link" title="mclmli.operationdiamondv2main.team中的类">TeamJoinValidator.JoinValidationResult</a></li>
<li class="circle">mclmli.operationdiamondv2main.team.<a href="mclmli/operationdiamondv2main/team/TeamManager.html" class="type-name-link" title="mclmli.operationdiamondv2main.team中的类">TeamManager</a></li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="枚举类分层结构">枚举类分层结构</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="java.lang中的类或接口">Object</a>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Enum.html" class="type-name-link external-link" title="java.lang中的类或接口">Enum</a>&lt;E&gt; (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Comparable.html" title="java.lang中的类或接口" class="external-link">Comparable</a>&lt;T&gt;, java.lang.constant.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/constant/Constable.html" title="java.lang.constant中的类或接口" class="external-link">Constable</a>, java.io.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="java.io中的类或接口" class="external-link">Serializable</a>)
<ul>
<li class="circle">mclmli.operationdiamondv2main.device.<a href="mclmli/operationdiamondv2main/device/DeviceDetector.DeviceType.html" class="type-name-link" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.DeviceType</a></li>
<li class="circle">mclmli.operationdiamondv2main.device.<a href="mclmli/operationdiamondv2main/device/DeviceDetector.InputType.html" class="type-name-link" title="mclmli.operationdiamondv2main.device 中的枚举类">DeviceDetector.InputType</a></li>
<li class="circle">mclmli.operationdiamondv2main.team.<a href="mclmli/operationdiamondv2main/team/TeamApplication.ApplicationStatus.html" class="type-name-link" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamApplication.ApplicationStatus</a></li>
<li class="circle">mclmli.operationdiamondv2main.team.<a href="mclmli/operationdiamondv2main/team/TeamDeviceRestriction.html" class="type-name-link" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamDeviceRestriction</a></li>
<li class="circle">mclmli.operationdiamondv2main.team.<a href="mclmli/operationdiamondv2main/team/TeamDeviceType.html" class="type-name-link" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamDeviceType</a></li>
<li class="circle">mclmli.operationdiamondv2main.team.<a href="mclmli/operationdiamondv2main/team/TeamManager.JoinResult.html" class="type-name-link" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamManager.JoinResult</a></li>
<li class="circle">mclmli.operationdiamondv2main.team.<a href="mclmli/operationdiamondv2main/team/TeamStatus.html" class="type-name-link" title="mclmli.operationdiamondv2main.team 中的枚举类">TeamStatus</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
