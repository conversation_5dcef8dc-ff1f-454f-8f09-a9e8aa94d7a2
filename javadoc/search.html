<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Sun Jul 27 00:12:59 CST 2025 -->
<title>搜索</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-07-27">
<meta name="description" content="search">
<meta name="generator" content="javadoc/SearchWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="search-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html#search">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<h1 class="title">搜索</h1>
<div>
<input type="text" id="page-search-input" disabled placeholder="搜索">
<input type="reset" id="page-search-reset" disabled value="重置" style="margin: 6px;">
<details class="page-search-details">
<summary id="page-search-expand">其他资源</summary>
</details>
</div>
<div class="page-search-info">
<p><a href="help-doc.html#search">帮助页</a> 介绍了 JavaDoc 搜索的范围和语法。</p>
<p>您可以使用 &lt;ctrl&gt; 或 &lt;cmd&gt; 键与左箭头和右箭头键组合在此页面中的结果选项卡之间切换。</p>
<p>下面的 URL 模板可用于在支持此功能的浏览器中将此页面配置为搜索引擎。已经对其进行了测试以在 Google Chrome 和 Mozilla Firefox 中使用。请注意，其他浏览器可能不支持此功能或需要不同的 URL 格式。</p>
<span id="page-search-link">link</span><button class="copy" aria-label="复制 URL" id="page-search-copy"><img src="copy.svg" alt="复制 URL"><span data-copied="已复制！">复制</span></button>
<p>
<input type="checkbox" id="search-redirect" disabled>
<label for="search-redirect">重定向到第一个结果</label></p>
</div>
<p id="page-search-notify">正在加载搜索索引...</p>
<div id="result-section" style="display: none;">
<div id="result-container"></div>
<script type="text/javascript" src="search-page.js"></script>
</div>
</main>
</div>
</div>
</body>
</html>
