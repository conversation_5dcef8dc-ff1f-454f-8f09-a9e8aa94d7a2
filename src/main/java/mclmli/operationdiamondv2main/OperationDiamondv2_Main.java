package mclmli.operationdiamondv2main;

import mclmli.operationdiamondv2main.team.TeamManager;
import mclmli.operationdiamondv2main.team.TeamCommand;
import mclmli.operationdiamondv2main.device.DeviceManager;
import mclmli.operationdiamondv2main.team.TeamGUIManager;
import mclmli.operationdiamondv2main.team.TeamFormManager;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.scheduler.BukkitRunnable;

public final class OperationDiamondv2_Main extends JavaPlugin {

    private TeamManager teamManager;
    private TeamCommand teamCommand;
    private DeviceManager deviceManager;
    private TeamGUIManager teamGUIManager;
    private TeamFormManager teamFormManager;

    @Override
    public void onEnable() {
        // 初始化组队管理器
        teamManager = new TeamManager();
        teamManager.initialize();

        // 初始化设备管理器
        deviceManager = new DeviceManager(this, teamManager);
        getServer().getPluginManager().registerEvents(deviceManager, this);

        // 初始化GUI管理器
        teamGUIManager = new TeamGUIManager(this, teamManager, deviceManager);
        getServer().getPluginManager().registerEvents(teamGUIManager, this);

        // 初始化表单管理器
        teamFormManager = new TeamFormManager(this, teamManager, deviceManager);

        // 初始化并注册指令处理器
        teamCommand = new TeamCommand(teamManager, deviceManager, teamGUIManager, teamFormManager);
        // 注册指令和Tab补全
        if (getCommand("team") != null) {
            getCommand("team").setExecutor(teamCommand);
            getCommand("team").setTabCompleter(teamCommand);
        } else {
            getLogger().warning("无法注册team指令 - 指令未在plugin.yml中定义");
        }

        // 启动定时清理任务（每30秒清理一次过期邀请和申请）
        new BukkitRunnable() {
            @Override
            public void run() {
                teamManager.cleanupExpired();
            }
        }.runTaskTimer(this, 600L, 600L); // 30秒后开始，每30秒执行一次

        // 启动设备缓存清理任务（每2分钟清理一次离线玩家缓存）
        new BukkitRunnable() {
            @Override
            public void run() {
                deviceManager.clearOfflinePlayerCache();
            }
        }.runTaskTimer(this, 2400L, 2400L); // 2分钟后开始，每2分钟执行一次

        getLogger().info("OperationDiamondv2-Main 插件已启用");

        // 输出详细的跨平台支持状态
        if (teamManager.hasFullCrossPlatformSupport()) {
            getLogger().info("✓ 完整跨平台支持：Geyser + Floodgate");
            getLogger().info("  - 支持基岩版玩家组队");
            getLogger().info("  - 支持表单界面");
            getLogger().info("  - 支持详细设备信息检测");
        } else if (teamManager.isGeyserEnabled() || teamManager.isFloodgateEnabled()) {
            getLogger().info("⚠ 部分跨平台支持");
            if (teamManager.isGeyserEnabled()) {
                getLogger().info("  - Geyser已启用：基本基岩版支持");
            }
            if (teamManager.isFloodgateEnabled()) {
                getLogger().info("  - Floodgate已启用：增强基岩版功能");
            }
        } else {
            getLogger().info("○ 仅Java版支持");
        }

        getLogger().info("组队系统已启用，输入 /team 查看帮助");
        getLogger().info("通用设备检测系统已启用，支持所有平台玩家设备识别");
    }

    @Override
    public void onDisable() {
        getLogger().info("OperationDiamondv2-Main 插件已关闭");
    }

    /**
     * 获取组队管理器实例
     * @return TeamManager实例
     */
    public TeamManager getTeamManager() {
        return teamManager;
    }

    /**
     * 获取设备管理器实例
     * @return DeviceManager实例
     */
    public DeviceManager getDeviceManager() {
        return deviceManager;
    }

    /**
     * 获取GUI管理器实例
     * @return TeamGUIManager实例
     */
    public TeamGUIManager getTeamGUIManager() {
        return teamGUIManager;
    }

    /**
     * 获取表单管理器实例
     * @return TeamFormManager实例
     */
    public TeamFormManager getTeamFormManager() {
        return teamFormManager;
    }
}
