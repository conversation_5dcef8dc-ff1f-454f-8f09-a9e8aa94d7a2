package mclmli.operationdiamondv2main.device;

import org.geysermc.floodgate.api.player.FloodgatePlayer;
import mclmli.operationdiamondv2main.team.TeamManager;

import java.util.UUID;

/**
 * 简化设备检测器 - 只检测基岩版/Java版和触屏/非触屏
 */
public class DeviceDetector {

    /**
     * 设备类型枚举 - 简化版
     */
    public enum DeviceType {
        JAVA("Java版", "☕"),
        BEDROCK("基岩版", "🪨");

        private final String displayName;
        private final String emoji;

        DeviceType(String displayName, String emoji) {
            this.displayName = displayName;
            this.emoji = emoji;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getEmoji() {
            return emoji;
        }
    }

    /**
     * 输入方式枚举 - 简化版
     */
    public enum InputType {
        TOUCHSCREEN("触屏", "👆"),
        NON_TOUCHSCREEN("非触屏", "⌨️");

        private final String displayName;
        private final String emoji;

        InputType(String displayName, String emoji) {
            this.displayName = displayName;
            this.emoji = emoji;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getEmoji() {
            return emoji;
        }
    }

    /**
     * 玩家设备信息类 - 简化版
     */
    public static class PlayerDeviceInfo {
        private final UUID playerId;
        private final DeviceType deviceType;
        private final InputType inputType;
        private final String rawDeviceOs;
        private final String rawInputMode;

        public PlayerDeviceInfo(UUID playerId, DeviceType deviceType, InputType inputType,
                              String rawDeviceOs, String rawInputMode) {
            this.playerId = playerId;
            this.deviceType = deviceType;
            this.inputType = inputType;
            this.rawDeviceOs = rawDeviceOs;
            this.rawInputMode = rawInputMode;
        }

        public UUID getPlayerId() { return playerId; }
        public DeviceType getDeviceType() { return deviceType; }
        public InputType getInputType() { return inputType; }
        public String getRawDeviceOs() { return rawDeviceOs; }
        public String getRawInputMode() { return rawInputMode; }

        /**
         * 是否为基岩版玩家
         */
        public boolean isBedrockPlayer() {
            return deviceType == DeviceType.BEDROCK;
        }

        /**
         * 是否使用触屏操作
         */
        public boolean isTouchInput() {
            return inputType == InputType.TOUCHSCREEN;
        }

        /**
         * 获取简短描述
         */
        public String getShortDescription() {
            return String.format("%s %s | %s %s",
                deviceType.getEmoji(), deviceType.getDisplayName(),
                inputType.getEmoji(), inputType.getDisplayName());
        }

        /**
         * 获取完整描述
         */
        public String getFullDescription() {
            return getShortDescription();
        }

        /**
         * 获取详细信息
         */
        public String getDetailedInfo() {
            return String.format("设备: %s\n输入: %s\n原始系统: %s\n原始输入: %s",
                deviceType.getDisplayName(), inputType.getDisplayName(), rawDeviceOs, rawInputMode);
        }
    }

    /**
     * 检测玩家设备信息
     * @param playerId 玩家UUID
     * @param teamManager 组队管理器（用于检查跨平台API状态）
     * @return 玩家设备信息
     */
    public static PlayerDeviceInfo detectPlayerDevice(UUID playerId, TeamManager teamManager) {
        // 检查是否为基岩版玩家
        if (!teamManager.isBedrockPlayer(playerId)) {
            return new PlayerDeviceInfo(playerId, DeviceType.JAVA, InputType.NON_TOUCHSCREEN,
                "JAVA", "KEYBOARD_MOUSE");
        }

        // 获取Floodgate玩家信息
        FloodgatePlayer floodgatePlayer = teamManager.getFloodgatePlayer(playerId);
        if (floodgatePlayer == null) {
            // 无法获取详细信息时，默认为基岩版非触屏
            return new PlayerDeviceInfo(playerId, DeviceType.BEDROCK, InputType.NON_TOUCHSCREEN,
                "UNKNOWN", "UNKNOWN");
        }

        // 获取原始信息
        String rawOs = floodgatePlayer.getDeviceOs().toString();
        String rawInput = floodgatePlayer.getInputMode().toString();

        // 检测输入方式（修复触屏检测bug）
        InputType inputType = detectInputType(floodgatePlayer);

        return new PlayerDeviceInfo(playerId, DeviceType.BEDROCK, inputType, rawOs, rawInput);
    }

    /**
     * 检测输入方式 - 完全基于操作系统判断，忽略不准确的输入模式
     */
    private static InputType detectInputType(FloodgatePlayer player) {
        String deviceOs = player.getDeviceOs().toString().toUpperCase();

        // 调试日志：只显示设备OS，因为输入模式不可靠
        System.out.println("DEBUG - Player: " + player.getUsername() +
                         ", DeviceOS: '" + deviceOs + "' -> " +
                         (isOSMobileDevice(deviceOs) ? "TOUCHSCREEN" : "NON_TOUCHSCREEN"));

        // 完全基于操作系统判断：移动设备 = 触屏，其他 = 非触屏
        return isOSMobileDevice(deviceOs) ? InputType.TOUCHSCREEN : InputType.NON_TOUCHSCREEN;
    }

    /**
     * 根据操作系统判断是否为移动设备
     */
    private static boolean isOSMobileDevice(String deviceOs) {
        // 移动设备操作系统列表
        return "ANDROID".equals(deviceOs) ||
               "IOS".equals(deviceOs) ||
               deviceOs.contains("MOBILE") ||
               deviceOs.contains("PHONE");
    }

    /**
     * 根据设备类型推荐功能
     */
    public static String getDeviceRecommendations(PlayerDeviceInfo deviceInfo) {
        StringBuilder recommendations = new StringBuilder();

        if (deviceInfo.isTouchInput()) {
            recommendations.append("👆 触屏用户建议:\n");
            recommendations.append("- 使用表单界面代替聊天指令\n");
            recommendations.append("- 启用触屏优化功能\n");
        } else {
            recommendations.append("⌨️ 非触屏用户建议:\n");
            recommendations.append("- 可以使用键盘快捷键\n");
            recommendations.append("- 支持复杂的聊天指令\n");
        }

        if (deviceInfo.isBedrockPlayer()) {
            recommendations.append("🪨 基岩版用户:\n");
            recommendations.append("- 部分功能可能受限\n");
        } else {
            recommendations.append("☕ Java版用户:\n");
            recommendations.append("- 支持所有功能\n");
        }

        return recommendations.toString();
    }
}
