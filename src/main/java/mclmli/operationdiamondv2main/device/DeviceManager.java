package mclmli.operationdiamondv2main.device;

import mclmli.operationdiamondv2main.OperationDiamondv2_Main;
import mclmli.operationdiamondv2main.team.TeamManager;
import mclmli.operationdiamondv2main.team.Team;
import net.md_5.bungee.api.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 设备管理器 - 管理所有玩家的设备信息
 * 不依赖于特定功能模块，为整个插件提供设备检测服务
 */
public class DeviceManager implements Listener {

    private final OperationDiamondv2_Main plugin;
    private final TeamManager teamManager;

    // 缓存所有玩家的设备信息
    private final Map<UUID, DeviceDetector.PlayerDeviceInfo> deviceInfoCache = new ConcurrentHashMap<>();

    public DeviceManager(OperationDiamondv2_Main plugin, TeamManager teamManager) {
        this.plugin = plugin;
        this.teamManager = teamManager;
    }

    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();

        // 延迟1秒检测设备信息（确保跨平台API数据已加载）
        new BukkitRunnable() {
            @Override
            public void run() {
                detectAndCachePlayerDevice(player);
            }
        }.runTaskLater(plugin, 20L);
    }

    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        UUID playerId = event.getPlayer().getUniqueId();
        // 玩家离线时清理缓存
        deviceInfoCache.remove(playerId);
    }

    /**
     * 检测并缓存玩家设备信息
     * @param player 玩家
     */
    private void detectAndCachePlayerDevice(Player player) {
        if (!player.isOnline()) {
            return;
        }

        try {
            DeviceDetector.PlayerDeviceInfo deviceInfo =
                DeviceDetector.detectPlayerDevice(player.getUniqueId(), teamManager);

            if (deviceInfo != null) {
                deviceInfoCache.put(player.getUniqueId(), deviceInfo);

                // 发送个性化欢迎消息
                sendDeviceWelcomeMessage(player, deviceInfo);

                // 记录到控制台
                plugin.getLogger().info(String.format("玩家 %s 设备信息: %s",
                    player.getName(), deviceInfo.getFullDescription()));
            }
        } catch (Exception e) {
            plugin.getLogger().warning("检测玩家 " + player.getName() + " 的设备信息时发生错误: " + e.getMessage());
        }
    }

    /**
     * 发送个性化设备欢迎消息
     */
    private void sendDeviceWelcomeMessage(Player player, DeviceDetector.PlayerDeviceInfo deviceInfo) {
        // 设置设备昵称前缀
        setDeviceNicknamePrefix(player, deviceInfo);
        
        player.sendMessage("");
        player.sendMessage(org.bukkit.ChatColor.GOLD + "=== 欢迎来到服务器 ===");
        player.sendMessage(org.bukkit.ChatColor.YELLOW + "检测到你的设备: " + org.bukkit.ChatColor.WHITE + deviceInfo.getFullDescription());

        // 根据设备类型提供个性化提示
        if (deviceInfo.isBedrockPlayer()) {
            player.sendMessage(org.bukkit.ChatColor.GREEN + "✓ 跨平台功能已启用");

            if (deviceInfo.isTouchInput()) {
                player.sendMessage(org.bukkit.ChatColor.AQUA + "👆 触屏操作优化:");
                player.sendMessage(org.bukkit.ChatColor.GRAY + "  - 支持触屏友好的操作界面");
                player.sendMessage(org.bukkit.ChatColor.GRAY + "  - 提供图形化菜单选项");
                if (teamManager.hasFullCrossPlatformSupport()) {
                    player.sendMessage(org.bukkit.ChatColor.GRAY + "  - 可使用表单界面进行复杂操作");
                }
            } else {
                player.sendMessage(org.bukkit.ChatColor.AQUA + "⌨️ 非触屏操作体验:");
                player.sendMessage(org.bukkit.ChatColor.GRAY + "  - 支持键盘鼠标快捷操作");
                player.sendMessage(org.bukkit.ChatColor.GRAY + "  - 提供完整指令功能");
            }
        } else {
            player.sendMessage(org.bukkit.ChatColor.BLUE + "☕ Java版完整功能:");
            player.sendMessage(org.bukkit.ChatColor.GRAY + "  - 支持所有高级功能");
            player.sendMessage(org.bukkit.ChatColor.GRAY + "  - 完整的指令系统");
            player.sendMessage(org.bukkit.ChatColor.GRAY + "  - 最佳的游戏体验");
        }

        player.sendMessage("");
        player.sendMessage(org.bukkit.ChatColor.AQUA + "你的昵称已根据设备类型自动设置前缀");
        player.sendMessage("");
    }

    /**
     * 根据设备类型为玩家设置昵称前缀
     */
    private void setDeviceNicknamePrefix(Player player, DeviceDetector.PlayerDeviceInfo deviceInfo) {
        String originalName = player.getName();
        String prefix;
        String color;

        if (deviceInfo.isBedrockPlayer()) {
            if (deviceInfo.isTouchInput()) {
                prefix = "🪨👆"; // 基岩版触屏
                color = org.bukkit.ChatColor.GREEN.toString();
            } else {
                prefix = "🪨⌨️"; // 基岩版非触屏
                color = org.bukkit.ChatColor.AQUA.toString();
            }
        } else {
            prefix = "☕"; // Java版
            color = org.bukkit.ChatColor.DARK_AQUA.toString();
        }

        // 设置带前缀的显示名称
        String displayName = color + "[" + prefix + "] " + org.bukkit.ChatColor.WHITE + originalName;
        player.setDisplayName(displayName);
        
        // 也设置玩家列表中的名称
        player.setPlayerListName(displayName);
        
        // 记录到控制台
        plugin.getLogger().info("为玩家 " + originalName + " 设置设备前缀: " + prefix + " (" + deviceInfo.getShortDescription() + ")");
    }

    /**
     * 获取设备类型对应的前缀
     * @param deviceInfo 设备信息
     * @return 前缀字符串
     */
    public String getDevicePrefix(DeviceDetector.PlayerDeviceInfo deviceInfo) {
        if (deviceInfo == null) {
            return "❓"; // 未知设备
        }

        if (deviceInfo.isBedrockPlayer()) {
            if (deviceInfo.isTouchInput()) {
                return "🪨👆"; // 基岩版触屏
            } else {
                return "🪨⌨️"; // 基岩版非触屏
            }
        } else {
            return "☕"; // Java版
        }
    }

    /**
     * 清理离线玩家的设备缓存
     * 由定时任务调用，清理不在线玩家的缓存数据
     */
    public void clearOfflinePlayerCache() {
        int initialSize = deviceInfoCache.size();
        int removedCount = 0;

        // 遍历缓存，移除离线玩家的数据
        deviceInfoCache.entrySet().removeIf(entry -> {
            UUID playerId = entry.getKey();
            Player player = plugin.getServer().getPlayer(playerId);
            boolean shouldRemove = player == null || !player.isOnline();
            return shouldRemove;
        });

        removedCount = initialSize - deviceInfoCache.size();

        if (removedCount > 0) {
            plugin.getLogger().info(String.format("设备缓存清理完成: 移除了 %d 个离线玩家的缓存数据 (剩余: %d)",
                removedCount, deviceInfoCache.size()));
        }
    }

    /**
     * 获取玩家的设备信息
     * @param playerId 玩家UUID
     * @return 设备信息，如果不存在则返回null
     */
    public DeviceDetector.PlayerDeviceInfo getPlayerDeviceInfo(UUID playerId) {
        return deviceInfoCache.get(playerId);
    }

    /**
     * 获取在线玩家的设备信息，如果缓存中没有则重新检测
     * @param player 在线玩家
     * @return 设备信息
     */
    public DeviceDetector.PlayerDeviceInfo getOrDetectPlayerDeviceInfo(Player player) {
        DeviceDetector.PlayerDeviceInfo cachedInfo = deviceInfoCache.get(player.getUniqueId());

        if (cachedInfo == null && player.isOnline()) {
            // 如果缓存中没有且玩家在线，重新检测
            try {
                cachedInfo = DeviceDetector.detectPlayerDevice(player.getUniqueId(), teamManager);
                if (cachedInfo != null) {
                    deviceInfoCache.put(player.getUniqueId(), cachedInfo);
                }
            } catch (Exception e) {
                plugin.getLogger().warning("重新检测玩家 " + player.getName() + " 的设备信息时发生错误: " + e.getMessage());
            }
        }

        return cachedInfo;
    }

    /**
     * 获取设备缓存的统计信息
     * @return 包含缓存统计信息的Map
     */
    public Map<String, Object> getCacheStatistics() {
        Map<String, Object> stats = new ConcurrentHashMap<>();
        stats.put("totalCached", deviceInfoCache.size());

        // 统计各种设备类型的数量
        int javaPlayers = 0;
        int bedrockPlayers = 0;
        int touchscreenDevices = 0;
        int nonTouchscreenDevices = 0;

        for (DeviceDetector.PlayerDeviceInfo info : deviceInfoCache.values()) {
            if (info.isBedrockPlayer()) {
                bedrockPlayers++;
            } else {
                javaPlayers++;
            }

            if (info.isTouchInput()) {
                touchscreenDevices++;
            } else {
                nonTouchscreenDevices++;
            }
        }

        stats.put("javaPlayers", javaPlayers);
        stats.put("bedrockPlayers", bedrockPlayers);
        stats.put("touchscreenDevices", touchscreenDevices);
        stats.put("nonTouchscreenDevices", nonTouchscreenDevices);

        return stats;
    }

    /**
     * 手动刷新玩家的设备信息
     * @param player 要刷新的玩家
     * @return 是否成功刷新
     */
    public boolean refreshPlayerDeviceInfo(Player player) {
        if (!player.isOnline()) {
            return false;
        }

        try {
            DeviceDetector.PlayerDeviceInfo newInfo =
                DeviceDetector.detectPlayerDevice(player.getUniqueId(), teamManager);

            if (newInfo != null) {
                deviceInfoCache.put(player.getUniqueId(), newInfo);
                plugin.getLogger().info("已刷新玩家 " + player.getName() + " 的设备信息: " + newInfo.getFullDescription());
                return true;
            }
        } catch (Exception e) {
            plugin.getLogger().warning("刷新玩家 " + player.getName() + " 的设备信息时发生错误: " + e.getMessage());
        }

        return false;
    }
}
