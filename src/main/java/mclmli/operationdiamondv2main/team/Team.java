package mclmli.operationdiamondv2main.team;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 队伍数据类
 * 包含队伍的所有信息和成员管理
 */
public class Team {

    private final String teamId;
    private String teamName;
    private UUID leaderId;
    private final Set<UUID> members;
    private TeamStatus status;
    private TeamDeviceRestriction deviceRestriction; // 新增：设备限制
    private String password;
    private final LocalDateTime createTime;
    private int maxMembers;

    /**
     * 创建新队伍
     * @param teamId 队伍ID
     * @param teamName 队伍名称
     * @param leaderId 队长UUID
     */
    public Team(String teamId, String teamName, UUID leaderId) {
        this.teamId = teamId;
        this.teamName = teamName;
        this.leaderId = leaderId;
        this.members = new HashSet<>();
        this.members.add(leaderId); // 队长自动加入成员列表
        this.status = TeamStatus.OPEN; // 默认公开状态
        this.deviceRestriction = TeamDeviceRestriction.getDefault(); // 使用默认的混合模式
        this.createTime = LocalDateTime.now();
        this.maxMembers = 4; // 修改队伍人数上限为4人
    }

    // Getters and Setters

    public String getTeamId() {
        return teamId;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public UUID getLeaderId() {
        return leaderId;
    }

    public void setLeaderId(UUID leaderId) {
        this.leaderId = leaderId;
    }

    public Set<UUID> getMembers() {
        return new HashSet<>(members);
    }

    public TeamStatus getStatus() {
        return status;
    }

    public void setStatus(TeamStatus status) {
        this.status = status;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public int getMaxMembers() {
        return maxMembers;
    }

    public void setMaxMembers(int maxMembers) {
        this.maxMembers = maxMembers;
    }

    public TeamDeviceRestriction getDeviceRestriction() {
        return deviceRestriction;
    }

    public void setDeviceRestriction(TeamDeviceRestriction deviceRestriction) {
        this.deviceRestriction = deviceRestriction;
    }

    // 队伍管理方法

    /**
     * 添加成员到队伍
     * @param playerId 玩家UUID
     * @return 是否成功添加
     */
    public boolean addMember(UUID playerId) {
        if (members.size() >= maxMembers) {
            return false; // 队伍已满
        }
        return members.add(playerId);
    }

    /**
     * 从队伍中移除成员
     * @param playerId 玩家UUID
     * @return 是否成功移除
     */
    public boolean removeMember(UUID playerId) {
        // 如果是队长，不能直接移除
        if (playerId.equals(leaderId)) {
            return false;
        }

        return members.remove(playerId);
    }

    /**
     * 检查玩家是否是队员
     * @param playerId 玩家UUID
     * @return 是否是队员
     */
    public boolean isMember(UUID playerId) {
        return members.contains(playerId);
    }

    /**
     * 检查玩家是否是队长
     * @param playerId 玩家UUID
     * @return 是否是队长
     */
    public boolean isLeader(UUID playerId) {
        return leaderId.equals(playerId);
    }

    /**
     * 获取当前队伍人数
     * @return 队伍人数
     */
    public int getMemberCount() {
        return members.size();
    }

    /**
     * 检查队伍是否已满
     * @return 是否已满
     */
    public boolean isFull() {
        return members.size() >= maxMembers;
    }

    /**
     * 转移队长权限
     * @param newLeaderId 新队长UUID
     * @return 是否成功转移
     */
    public boolean transferLeadership(UUID newLeaderId) {
        if (!members.contains(newLeaderId)) {
            return false; // 新队长必须是队员
        }
        this.leaderId = newLeaderId;
        return true;
    }

    /**
     * 验证密码
     * @param inputPassword 输入的密码
     * @return 密码是否正确
     */
    public boolean verifyPassword(String inputPassword) {
        if (password == null) {
            return inputPassword == null || inputPassword.isEmpty();
        }
        return password.equals(inputPassword);
    }

    // 设备相关方法

    /**
     * 检查玩家设备是否允许加入队伍
     * @param playerId 玩家UUID
     * @param deviceManager 设备管理器
     * @return 是否允许加入
     */
    public boolean isDeviceAllowed(UUID playerId, mclmli.operationdiamondv2main.device.DeviceManager deviceManager) {
        // 将UUID转换为Player对象
        org.bukkit.entity.Player player = org.bukkit.Bukkit.getPlayer(playerId);
        if (player == null) {
            // 玩家不在线时，根据限制类型决定（通常拒绝加入）
            return deviceRestriction != TeamDeviceRestriction.TOUCHSCREEN_ONLY;
        }

        mclmli.operationdiamondv2main.device.DeviceDetector.PlayerDeviceInfo deviceInfo =
            deviceManager.getOrDetectPlayerDeviceInfo(player);

        if (deviceInfo == null) {
            // 无法检测设备信息时，根据限制类型决定
            return deviceRestriction != TeamDeviceRestriction.TOUCHSCREEN_ONLY;
        }

        boolean isTouchscreen = deviceInfo.isTouchInput();

        switch (deviceRestriction) {
            case TOUCHSCREEN_ONLY:
                return isTouchscreen;
            case NON_TOUCHSCREEN_ONLY:
                return !isTouchscreen;
            case MIXED:
                return true; // 混合模式允许所有设备加入，但会发送警告
            default:
                return true;
        }
    }

    /**
     * 智能匹配检查
     * @param playerId 玩家UUID
     * @param deviceInfo 玩家设备信息
     * @param deviceManager 设备管理器
     * @return 是否允许加入
     */
    private boolean smartMatchingCheck(UUID playerId,
                                     mclmli.operationdiamondv2main.device.DeviceDetector.PlayerDeviceInfo deviceInfo,
                                     mclmli.operationdiamondv2main.device.DeviceManager deviceManager) {
        boolean isTouchscreen = deviceInfo.isTouchInput();
        boolean teamIsTouchscreenOnly = isTouchscreenOnlyTeam(deviceManager);

        // 如果队伍当前是纯触屏，优先接受触屏玩家
        if (teamIsTouchscreenOnly) {
            return true; // 允许所有玩家加入，但会有警告
        }

        // 如果队伍已有其他设备玩家，则无限制
        return true;
    }

    /**
     * 检查队伍是否是纯触屏队伍
     * @param deviceManager 设备管理器
     * @return 是否是纯触屏队伍
     */
    public boolean isTouchscreenOnlyTeam(mclmli.operationdiamondv2main.device.DeviceManager deviceManager) {
        for (UUID memberId : members) {
            org.bukkit.entity.Player player = org.bukkit.Bukkit.getPlayer(memberId);
            if (player != null && player.isOnline()) {
                mclmli.operationdiamondv2main.device.DeviceDetector.PlayerDeviceInfo deviceInfo =
                    deviceManager.getOrDetectPlayerDeviceInfo(player);

                if (deviceInfo != null && !deviceInfo.isTouchInput()) {
                    return false; // 找到非触屏玩家
                }
            }
        }
        return !members.isEmpty(); // 如果队伍不为空且所有玩家都是触屏，返回true
    }

    /**
     * 获取队伍设备类型
     * @param deviceManager 设备管理器
     * @return 队伍设备类型
     */
    public TeamDeviceType getTeamDeviceType(mclmli.operationdiamondv2main.device.DeviceManager deviceManager) {
        return isTouchscreenOnlyTeam(deviceManager) ? TeamDeviceType.TOUCHSCREEN_ONLY : TeamDeviceType.MIXED_OR_OTHER;
    }

    @Override
    public String toString() {
        return String.format("Team{id='%s', name='%s', leader=%s, members=%d/%d, status=%s}",
            teamId, teamName, leaderId, members.size(), maxMembers, status.getDisplayName());
    }
}
