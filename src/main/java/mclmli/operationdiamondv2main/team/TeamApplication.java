package mclmli.operationdiamondv2main.team;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 队伍申请数据类
 * 处理队伍申请相关信息
 */
public class TeamApplication {

    private final String applicationId;
    private final String teamId;
    private final UUID applicantId; // 申请者
    private final String message; // 申请留言
    private final LocalDateTime applicationTime;
    private final LocalDateTime expireTime;
    private boolean expired;
    private ApplicationStatus status;

    /**
     * 申请状态枚举
     */
    public enum ApplicationStatus {
        PENDING("待处理"),
        APPROVED("已批准"),
        REJECTED("已拒绝"),
        EXPIRED("已过期");

        private final String displayName;

        ApplicationStatus(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    /**
     * 创建队伍申请
     * @param teamId 队伍ID
     * @param applicantId 申请者UUID
     * @param message 申请留言
     */
    public TeamApplication(String teamId, UUID applicantId, String message) {
        this.applicationId = UUID.randomUUID().toString();
        this.teamId = teamId;
        this.applicantId = applicantId;
        this.message = message != null ? message : "";
        this.applicationTime = LocalDateTime.now();
        this.expireTime = applicationTime.plusHours(24); // 24小时后过期
        this.expired = false;
        this.status = ApplicationStatus.PENDING;
    }

    public String getApplicationId() {
        return applicationId;
    }

    public String getTeamId() {
        return teamId;
    }

    public UUID getApplicantId() {
        return applicantId;
    }

    public String getMessage() {
        return message;
    }

    public LocalDateTime getApplicationTime() {
        return applicationTime;
    }

    public LocalDateTime getExpireTime() {
        return expireTime;
    }

    public ApplicationStatus getStatus() {
        return status;
    }

    public void setStatus(ApplicationStatus status) {
        this.status = status;
    }

    /**
     * 检查申请是否过期
     * @return 是否过期
     */
    public boolean isExpired() {
        if (!expired && LocalDateTime.now().isAfter(expireTime)) {
            expired = true;
            status = ApplicationStatus.EXPIRED;
        }
        return expired;
    }

    /**
     * 检查申请是否有效（未过期且处于待处理状态）
     * @return 是否有效
     */
    public boolean isValid() {
        return !isExpired() && status == ApplicationStatus.PENDING;
    }

    /**
     * 批准申请
     */
    public void approve() {
        this.status = ApplicationStatus.APPROVED;
    }

    /**
     * 拒绝申请
     */
    public void reject() {
        this.status = ApplicationStatus.REJECTED;
    }

    /**
     * 手动设置申请为过期
     */
    public void markExpired() {
        this.expired = true;
        this.status = ApplicationStatus.EXPIRED;
    }

    /**
     * 获取申请的简短描述
     * @return 申请描述
     */
    public String getShortDescription() {
        String messagePreview = message.length() > 20 ? message.substring(0, 20) + "..." : message;
        return String.format("申请时间: %s, 状态: %s%s",
            applicationTime.toString().substring(0, 19).replace('T', ' '),
            status.getDisplayName(),
            messagePreview.isEmpty() ? "" : ", 留言: " + messagePreview);
    }

    @Override
    public String toString() {
        return String.format("TeamApplication{id='%s', teamId='%s', applicant=%s, status=%s, expired=%b}",
            applicationId, teamId, applicantId, status, expired);
    }
}
