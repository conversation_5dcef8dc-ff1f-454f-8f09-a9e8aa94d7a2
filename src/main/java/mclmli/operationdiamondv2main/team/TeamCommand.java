package mclmli.operationdiamondv2main.team;

import org.bukkit.Bukkit;
import org.bukkit.Sound;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import net.md_5.bungee.api.ChatColor;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 组队指令处理器
 * 处理所有组队相关的指令
 */
public class TeamCommand implements CommandExecutor, TabCompleter {

    private final TeamManager teamManager;
    private final mclmli.operationdiamondv2main.device.DeviceManager deviceManager;
    private final TeamGUIManager teamGUIManager;
    private final TeamFormManager teamFormManager;

    public TeamCommand(TeamManager teamManager, mclmli.operationdiamondv2main.device.DeviceManager deviceManager,
                      TeamGUIManager teamGUIManager, TeamFormManager teamFormManager) {
        this.teamManager = teamManager;
        this.deviceManager = deviceManager;
        this.teamGUIManager = teamGUIManager;
        this.teamFormManager = teamFormManager;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "只有玩家才能使用组队指令！");
            return true;
        }

        Player player = (Player) sender;

        if (args.length == 0) {
            // 默认打开GUI界面
            openPlayerInterface(player);
            return true;
        }

        String subCommand = args[0].toLowerCase();

        switch (subCommand) {
            case "gui":
            case "ui":
            case "menu":
                openPlayerInterface(player);
                break;
            case "create":
            case "c":
                handleCreateTeam(player, args);
                break;
            case "join":
            case "j":
                handleJoinTeam(player, args);
                break;
            case "leave":
            case "quit":
            case "q":
                handleLeaveTeam(player);
                break;
            case "kick":
            case "k":
                handleKickMember(player, args);
                break;
            case "invite":
            case "i":
                handleInvitePlayer(player, args);
                break;
            case "accept":
            case "a":
                handleAcceptInvitation(player, args);
                break;
            case "deny":
            case "reject":
            case "d":
                handleDenyInvitation(player, args);
                break;
            case "apply":
                handleApplyTeam(player, args);
                break;
            case "approve":
                handleApproveApplication(player, args);
                break;
            case "transfer":
            case "leader":
                handleTransferLeadership(player, args);
                break;
            case "status":
            case "set":
                handleSetTeamStatus(player, args);
                break;
            case "info":
            case "list":
                handleTeamInfo(player, args);
                break;
            case "password":
            case "pwd":
                handleSetPassword(player, args);
                break;
            case "devices":
            case "stats":
                handleDeviceStats(player);
                break;
            case "restrict":
                handleDeviceRestriction(player, args);
                break;
            default:
                sendHelpMessage(player);
                break;
        }

        return true;
    }

    /**
     * 根据玩家平台打开相应的界面
     */
    private void openPlayerInterface(Player player) {
        if (teamFormManager.isBedrockPlayer(player)) {
            // 基岩版玩家打开表单UI
            teamFormManager.openMainMenu(player);
        } else {
            // Java版玩家打开GUI
            teamGUIManager.openMainMenu(player);
        }
    }

    /**
     * 处理创建队伍指令
     */
    private void handleCreateTeam(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage(ChatColor.RED + "用法: /team create <队伍名称>");
            return;
        }

        if (teamManager.getPlayerTeam(player.getUniqueId()) != null) {
            player.sendMessage(ChatColor.RED + "你已经在一个队伍中了！请先退出当前队伍。");
            return;
        }

        String teamName = String.join(" ", Arrays.copyOfRange(args, 1, args.length));

        if (teamName.length() > 20) {
            player.sendMessage(ChatColor.RED + "队伍名称不能超过20个字符！");
            return;
        }

        if (teamManager.isTeamNameExists(teamName)) {
            player.sendMessage(ChatColor.RED + "队伍名称已存在！请选择其他名称。");
            return;
        }

        Team team = teamManager.createTeam(teamName, player.getUniqueId());
        if (team != null) {
            player.sendMessage(ChatColor.GREEN + "成功创建队伍: " + ChatColor.YELLOW + teamName);
            player.sendMessage(ChatColor.GRAY + "队伍ID: " + team.getTeamId());

            // 显示平台信息
            String platformInfo = teamManager.getPlayerPlatformInfo(player.getUniqueId());
            player.sendMessage(ChatColor.GRAY + "你的平台: " + platformInfo);
        } else {
            player.sendMessage(ChatColor.RED + "创建队伍失败！");
        }
    }

    /**
     * 处理加入队伍指令
     */
    private void handleJoinTeam(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage(ChatColor.RED + "用法: /team join <队伍名称/ID> [密码]");
            return;
        }

        if (teamManager.getPlayerTeam(player.getUniqueId()) != null) {
            player.sendMessage(ChatColor.RED + "你已经在一个队伍中了！");
            return;
        }

        String teamIdentifier = args[1];
        String password = args.length > 2 ? args[2] : null;

        Team team = teamManager.findTeam(teamIdentifier);
        if (team == null) {
            player.sendMessage(ChatColor.RED + "找不到队伍: " + teamIdentifier);
            return;
        }

        // 检查队伍状态和加入条件
        TeamManager.JoinResult result;

        // 获取设备管理器进行设备检查
        mclmli.operationdiamondv2main.OperationDiamondv2_Main plugin =
            (mclmli.operationdiamondv2main.OperationDiamondv2_Main) org.bukkit.Bukkit.getPluginManager().getPlugin("OperationDiamondv2-Main");

        if (plugin != null && plugin.getDeviceManager() != null) {
            // 使用带设备检查的方法
            result = teamManager.joinTeam(player.getUniqueId(), team.getTeamId(), password, plugin.getDeviceManager());
        } else {
            // 降级到普通方法
            result = teamManager.joinTeam(player.getUniqueId(), team.getTeamId(), password);
        }

        switch (result) {
            case SUCCESS:
                player.sendMessage(ChatColor.GREEN + "成功加入队伍: " + ChatColor.YELLOW + team.getTeamName());
                // 通知队伍成员
                notifyTeamMembers(team, ChatColor.GREEN + player.getName() + " 加入了队伍！");
                playSpecialSound(player, "join");
                break;
            case TEAM_FULL:
                player.sendMessage(ChatColor.RED + "队伍已满！");
                playCommandSound(player, false);
                break;
            case WRONG_PASSWORD:
                player.sendMessage(ChatColor.RED + "密码错误！");
                playCommandSound(player, false);
                break;
            case INVITE_ONLY:
                player.sendMessage(ChatColor.RED + "该队伍仅限邀请加入！");
                playCommandSound(player, false);
                break;
            case APPLY_REQUIRED:
                // 自动创建申请
                teamManager.createApplication(team.getTeamId(), player.getUniqueId(), "");
                player.sendMessage(ChatColor.YELLOW + "已向队伍发送加入申请，等待队长审批...");
                // 通知队长和管理员
                notifyTeamModerators(team, ChatColor.YELLOW + player.getName() + " 申请加入队伍！使用 /team approve " + player.getName() + " 同意申请");
                playCommandSound(player, true);
                break;
            case ALREADY_MEMBER:
                player.sendMessage(ChatColor.RED + "你已经是该队伍的成员了！");
                playCommandSound(player, false);
                break;
            case TEAM_NOT_FOUND:
                player.sendMessage(ChatColor.RED + "队伍不存在！");
                playCommandSound(player, false);
                break;
            case DEVICE_NOT_ALLOWED:
                player.sendMessage(ChatColor.RED + "你的设备类型不符合队伍的设备限制要求！");
                // 显示队伍的设备限制信息
                TeamDeviceRestriction restriction = team.getDeviceRestriction();
                if (restriction != TeamDeviceRestriction.MIXED) {
                    player.sendMessage(ChatColor.YELLOW + "设备限制: " + ChatColor.WHITE + restriction.getDisplayName());
                    player.sendMessage(ChatColor.GRAY + restriction.getDescription());
                }
                playCommandSound(player, false);
                break;
        }
    }

    /**
     * 处理退出队伍指令
     */
    private void handleLeaveTeam(Player player) {
        Team team = teamManager.getPlayerTeam(player.getUniqueId());
        if (team == null) {
            player.sendMessage(ChatColor.RED + "你不在任何队伍中！");
            return;
        }

        if (team.isLeader(player.getUniqueId())) {
            // 队长退出，解散队伍
            teamManager.disbandTeam(team.getTeamId());
            player.sendMessage(ChatColor.YELLOW + "你退出了队伍，队伍已解散！");
            // 通知其他成员
            notifyTeamMembers(team, ChatColor.RED + "队长退出，队伍已解散！", player.getUniqueId());
            playSpecialSound(player, "leave");
        } else {
            // 普通成员退出
            teamManager.removeFromTeam(player.getUniqueId());
            player.sendMessage(ChatColor.YELLOW + "你已退出队伍: " + team.getTeamName());
            // 通知队伍成员
            notifyTeamMembers(team, ChatColor.YELLOW + player.getName() + " 退出了队伍！");
            playCommandSound(player, true);
        }
    }

    /**
     * 处理踢出成员指令
     */
    private void handleKickMember(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage(ChatColor.RED + "用法: /team kick <玩家名>");
            return;
        }

        Team team = teamManager.getPlayerTeam(player.getUniqueId());
        if (team == null) {
            player.sendMessage(ChatColor.RED + "你不在任何队伍中！");
            return;
        }

        if (!team.isLeader(player.getUniqueId())) {
            player.sendMessage(ChatColor.RED + "只有队长才能踢出成员！");
            return;
        }

        Player target = Bukkit.getPlayer(args[1]);
        if (target == null) {
            player.sendMessage(ChatColor.RED + "玩家不在线: " + args[1]);
            return;
        }

        if (!team.isMember(target.getUniqueId())) {
            player.sendMessage(ChatColor.RED + target.getName() + " 不在你的队伍中！");
            return;
        }

        if (team.isLeader(target.getUniqueId())) {
            player.sendMessage(ChatColor.RED + "不能踢出队长！");
            return;
        }

        teamManager.removeFromTeam(target.getUniqueId());
        player.sendMessage(ChatColor.GREEN + "成功踢出 " + target.getName());
        target.sendMessage(ChatColor.RED + "你被 " + player.getName() + " 踢出了队伍: " + team.getTeamName());
        // 通知其他成员
        notifyTeamMembers(team, ChatColor.RED + target.getName() + " 被踢出了队伍！", player.getUniqueId(), target.getUniqueId());
        playSpecialSound(player, "kick");
    }

    /**
     * 处理邀请玩家指令
     */
    private void handleInvitePlayer(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage(ChatColor.RED + "用法: /team invite <玩家名>");
            return;
        }

        Team team = teamManager.getPlayerTeam(player.getUniqueId());
        if (team == null) {
            player.sendMessage(ChatColor.RED + "你不在任何队伍中！");
            return;
        }

        if (!team.isLeader(player.getUniqueId())) {
            player.sendMessage(ChatColor.RED + "只有队长才能邀请玩家！");
            return;
        }

        Player target = Bukkit.getPlayer(args[1]);
        if (target == null) {
            player.sendMessage(ChatColor.RED + "玩家不在线: " + args[1]);
            return;
        }

        if (team.isMember(target.getUniqueId())) {
            player.sendMessage(ChatColor.RED + target.getName() + " 已经在队伍中了！");
            return;
        }

        if (teamManager.getPlayerTeam(target.getUniqueId()) != null) {
            player.sendMessage(ChatColor.RED + target.getName() + " 已经在其他队伍中了！");
            return;
        }

        if (team.isFull()) {
            player.sendMessage(ChatColor.RED + "队伍已满，无法邀请更多玩家！");
            return;
        }

        TeamInvitation invitation = teamManager.createInvitation(team.getTeamId(), player.getUniqueId(), target.getUniqueId());
        if (invitation != null) {
            player.sendMessage(ChatColor.GREEN + "成功邀请 " + target.getName() + " 加入队伍！");
            target.sendMessage(ChatColor.YELLOW + "你收到了来自 " + player.getName() + " 的队伍邀请:");
            target.sendMessage(ChatColor.YELLOW + "队伍: " + team.getTeamName());
            target.sendMessage(ChatColor.YELLOW + "使用 /team accept 接受邀请，/team deny 拒绝邀请");
            playSpecialSound(player, "invite");
        } else {
            player.sendMessage(ChatColor.RED + "邀请失败！该玩家可能已有待处理的邀请。");
        }
    }

    /**
     * 处理接受邀请指令
     */
    private void handleAcceptInvitation(Player player, String[] args) {
        TeamInvitation invitation = teamManager.getPlayerInvitation(player.getUniqueId());
        if (invitation == null) {
            player.sendMessage(ChatColor.RED + "你没有待处理的队伍邀请！");
            return;
        }

        if (!invitation.isValid()) {
            player.sendMessage(ChatColor.RED + "邀请已过期！");
            teamManager.removeInvitation(invitation.getInvitationId());
            return;
        }

        Team team = teamManager.getTeam(invitation.getTeamId());
        if (team == null) {
            player.sendMessage(ChatColor.RED + "队伍已不存在！");
            teamManager.removeInvitation(invitation.getInvitationId());
            return;
        }

        if (team.isFull()) {
            player.sendMessage(ChatColor.RED + "队伍已满！");
            teamManager.removeInvitation(invitation.getInvitationId());
            return;
        }

        if (teamManager.joinTeam(player.getUniqueId(), team.getTeamId(), null) == TeamManager.JoinResult.SUCCESS) {
            player.sendMessage(ChatColor.GREEN + "成功加入队伍: " + ChatColor.YELLOW + team.getTeamName());
            teamManager.removeInvitation(invitation.getInvitationId());

            // 通知邀请者和队伍成员
            Player inviter = Bukkit.getPlayer(invitation.getInviterId());
            if (inviter != null && inviter.isOnline()) {
                inviter.sendMessage(ChatColor.GREEN + player.getName() + " 接受了队伍邀请！");
            }
            notifyTeamMembers(team, ChatColor.GREEN + player.getName() + " 通过邀请加入了队伍！", player.getUniqueId());
            playCommandSound(player, true);
        } else {
            player.sendMessage(ChatColor.RED + "加入队伍失败！");
            playCommandSound(player, false);
        }
    }

    /**
     * 处理拒绝邀请指令
     */
    private void handleDenyInvitation(Player player, String[] args) {
        TeamInvitation invitation = teamManager.getPlayerInvitation(player.getUniqueId());
        if (invitation == null) {
            player.sendMessage(ChatColor.RED + "你没有待处理的队伍邀请！");
            return;
        }

        Team team = teamManager.getTeam(invitation.getTeamId());
        String teamName = team != null ? team.getTeamName() : "未知队伍";

        teamManager.removeInvitation(invitation.getInvitationId());
        player.sendMessage(ChatColor.YELLOW + "你拒绝了来自队伍 " + teamName + " 的邀请");

        // 通知邀请者
        Player inviter = Bukkit.getPlayer(invitation.getInviterId());
        if (inviter != null && inviter.isOnline()) {
            inviter.sendMessage(ChatColor.RED + player.getName() + " 拒绝了队伍邀请");
        }
    }

    /**
     * 处理申请加入队伍指令
     */
    private void handleApplyTeam(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage(ChatColor.RED + "用法: /team apply <队伍名称/ID> [申请留言]");
            return;
        }

        if (teamManager.getPlayerTeam(player.getUniqueId()) != null) {
            player.sendMessage(ChatColor.RED + "你已经在一个队伍中了！");
            return;
        }

        String teamIdentifier = args[1];
        String message = args.length > 2 ? String.join(" ", Arrays.copyOfRange(args, 2, args.length)) : "";

        Team team = teamManager.findTeam(teamIdentifier);
        if (team == null) {
            player.sendMessage(ChatColor.RED + "找不到队伍: " + teamIdentifier);
            return;
        }

        if (team.getStatus() != TeamStatus.APPLY_REQUIRED && team.getStatus() != TeamStatus.OPEN) {
            player.sendMessage(ChatColor.RED + "该队伍不接受申请！");
            return;
        }

        if (team.isFull()) {
            player.sendMessage(ChatColor.RED + "队伍已满！");
            return;
        }

        TeamApplication application = teamManager.createApplication(team.getTeamId(), player.getUniqueId(), message);
        if (application != null) {
            player.sendMessage(ChatColor.GREEN + "成功向队伍 " + team.getTeamName() + " 发送申请！");
            if (!message.isEmpty()) {
                player.sendMessage(ChatColor.GRAY + "申请留言: " + message);
            }

            // 通知队长和管理员
            String notifyMessage = ChatColor.YELLOW + player.getName() + " 申请加入队伍！";
            if (!message.isEmpty()) {
                notifyMessage += "\n" + ChatColor.GRAY + "留言: " + message;
            }
            notifyMessage += "\n" + ChatColor.YELLOW + "使用 /team approve " + player.getName() + " 同意申请";
            notifyTeamModerators(team, notifyMessage);
            playCommandSound(player, true);
        } else {
            player.sendMessage(ChatColor.RED + "申请失败！你可能已有待处理的申请。");
            playCommandSound(player, false);
        }
    }

    /**
     * 处理批准申请指令
     */
    private void handleApproveApplication(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage(ChatColor.RED + "用法: /team approve <玩家名>");
            return;
        }

        Team team = teamManager.getPlayerTeam(player.getUniqueId());
        if (team == null) {
            player.sendMessage(ChatColor.RED + "你不在任何队伍中！");
            return;
        }

        if (!team.isLeader(player.getUniqueId())) {
            player.sendMessage(ChatColor.RED + "只有队长才能批准申请！");
            return;
        }

        Player applicant = Bukkit.getPlayer(args[1]);
        if (applicant == null) {
            player.sendMessage(ChatColor.RED + "玩家不在线: " + args[1]);
            return;
        }

        TeamApplication application = teamManager.getTeamApplication(team.getTeamId(), applicant.getUniqueId());
        if (application == null) {
            player.sendMessage(ChatColor.RED + "没有找到 " + applicant.getName() + " 的申请！");
            return;
        }

        if (team.isFull()) {
            player.sendMessage(ChatColor.RED + "队伍已满，无法批准申请！");
            return;
        }

        if (teamManager.joinTeam(applicant.getUniqueId(), team.getTeamId(), null) == TeamManager.JoinResult.SUCCESS) {
            teamManager.removeApplication(application.getApplicationId());
            player.sendMessage(ChatColor.GREEN + "成功批准 " + applicant.getName() + " 的申请！");
            applicant.sendMessage(ChatColor.GREEN + "你的队伍申请已被 " + player.getName() + " 批准！");
            applicant.sendMessage(ChatColor.GREEN + "成功加入队伍: " + ChatColor.YELLOW + team.getTeamName());

            // 通知其他队伍成员
            notifyTeamMembers(team, ChatColor.GREEN + applicant.getName() + " 通过申请加入了队伍！", player.getUniqueId(), applicant.getUniqueId());
            playSpecialSound(player, "approve");
        } else {
            player.sendMessage(ChatColor.RED + "批准申请失败！");
            playCommandSound(player, false);
        }
    }

    /**
     * 处理转移队长指令
     */
    private void handleTransferLeadership(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage(ChatColor.RED + "用法: /team transfer <玩家名>");
            return;
        }

        Team team = teamManager.getPlayerTeam(player.getUniqueId());
        if (team == null) {
            player.sendMessage(ChatColor.RED + "你不在任何队伍中！");
            return;
        }

        if (!team.isLeader(player.getUniqueId())) {
            player.sendMessage(ChatColor.RED + "只有队长才能转移队长权限！");
            return;
        }

        Player newLeader = Bukkit.getPlayer(args[1]);
        if (newLeader == null) {
            player.sendMessage(ChatColor.RED + "玩家不在线: " + args[1]);
            return;
        }

        if (!team.isMember(newLeader.getUniqueId())) {
            player.sendMessage(ChatColor.RED + newLeader.getName() + " 不在你的队伍中！");
            return;
        }

        if (team.transferLeadership(newLeader.getUniqueId())) {
            player.sendMessage(ChatColor.GREEN + "成功将队长权限转移给 " + newLeader.getName());
            newLeader.sendMessage(ChatColor.GOLD + "你现在是队伍 " + team.getTeamName() + " 的队长！");

            // 通知队伍成员
            notifyTeamMembers(team, ChatColor.GOLD + newLeader.getName() + " 现在是队伍的新队长！", player.getUniqueId(), newLeader.getUniqueId());
            playSpecialSound(player, "transfer");
        } else {
            player.sendMessage(ChatColor.RED + "转移队长权限失败！");
            playCommandSound(player, false);
        }
    }

    /**
     * 处理设置队伍状态指令
     */
    private void handleSetTeamStatus(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage(ChatColor.RED + "用法: /team status <open|apply|invite|password>");
            player.sendMessage(ChatColor.GRAY + "open - 公开，任何人都可以加入");
            player.sendMessage(ChatColor.GRAY + "apply - 需要申请，玩家申请后需要队长同意");
            player.sendMessage(ChatColor.GRAY + "invite - 仅邀请，只有被邀请的玩家才能加入");
            player.sendMessage(ChatColor.GRAY + "password - 需要密码，玩家需要输入正确密码才能加入");
            player.sendMessage(ChatColor.YELLOW + "提示: 使用 /team restrict 来设置设备限制");
            return;
        }

        Team team = teamManager.getPlayerTeam(player.getUniqueId());
        if (team == null) {
            player.sendMessage(ChatColor.RED + "你不在任何队伍中！");
            return;
        }

        if (!team.isLeader(player.getUniqueId())) {
            player.sendMessage(ChatColor.RED + "只有队长才能设置队伍状态！");
            return;
        }

        String statusStr = args[1].toLowerCase();
        TeamStatus newStatus;

        switch (statusStr) {
            case "open":
            case "公开":
                newStatus = TeamStatus.OPEN;
                break;
            case "apply":
            case "申请":
                newStatus = TeamStatus.APPLY_REQUIRED;
                break;
            case "invite":
            case "邀请":
                newStatus = TeamStatus.INVITE_ONLY;
                break;
            case "password":
            case "密码":
                newStatus = TeamStatus.PASSWORD_REQUIRED;
                if (team.getPassword() == null || team.getPassword().isEmpty()) {
                    player.sendMessage(ChatColor.RED + "请先使用 /team password <密码> 设置队伍密码！");
                    return;
                }
                break;
            default:
                player.sendMessage(ChatColor.RED + "无效的队伍状态: " + statusStr);
                player.sendMessage(ChatColor.YELLOW + "提示: 设备限制请使用 /team restrict 命令");
                return;
        }

        team.setStatus(newStatus);
        player.sendMessage(ChatColor.GREEN + "队伍状态已设置为: " + ChatColor.YELLOW + newStatus.getDisplayName());
        player.sendMessage(ChatColor.GRAY + newStatus.getDescription());

        // 通知队伍成员
        notifyTeamMembers(team, ChatColor.YELLOW + "队长将队伍状态设置为: " + newStatus.getDisplayName(), player.getUniqueId());
    }

    /**
     * 处理查看队伍信息指令
     */
    private void handleTeamInfo(Player player, String[] args) {
        Team team = teamManager.getPlayerTeam(player.getUniqueId());
        if (team == null) {
            player.sendMessage(ChatColor.RED + "你不在任何队伍中！");
            return;
        }

        player.sendMessage(ChatColor.GOLD + "=== 队伍信息 ===");
        player.sendMessage(ChatColor.YELLOW + "队伍名称: " + ChatColor.WHITE + team.getTeamName());
        player.sendMessage(ChatColor.YELLOW + "队伍ID: " + ChatColor.WHITE + team.getTeamId());
        player.sendMessage(ChatColor.YELLOW + "队长: " + ChatColor.WHITE + getPlayerName(team.getLeaderId()));
        player.sendMessage(ChatColor.YELLOW + "成员数量: " + ChatColor.WHITE + team.getMemberCount() + "/" + team.getMaxMembers());
        player.sendMessage(ChatColor.YELLOW + "队伍状态: " + ChatColor.WHITE + team.getStatus().getDisplayName());

        // 显示设备限制信息
        TeamDeviceRestriction deviceRestriction = team.getDeviceRestriction();
        if (deviceRestriction != TeamDeviceRestriction.MIXED) {
            player.sendMessage(ChatColor.YELLOW + "设备限制: " + ChatColor.WHITE + deviceRestriction.getDisplayName());
        }

        // 显示成员列表
        player.sendMessage(ChatColor.YELLOW + "成员列表:");
        for (UUID memberId : team.getMembers()) {
            String memberName = getPlayerName(memberId);
            String role = "";
            if (team.isLeader(memberId)) {
                role = ChatColor.GOLD + " [队长]";
            }

            Player member = Bukkit.getPlayer(memberId);
            String onlineStatus = (member != null && member.isOnline()) ? ChatColor.GREEN + " [在线]" : ChatColor.GRAY + " [离线]";
            String platformInfo = teamManager.getPlayerPlatformInfo(memberId);

            player.sendMessage(ChatColor.WHITE + "  - " + memberName + role + onlineStatus + ChatColor.GRAY + " (" + platformInfo + ")");
        }
    }

    /**
     * 处理设置密码指令
     */
    private void handleSetPassword(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage(ChatColor.RED + "用法: /team password <密码>");
            return;
        }

        Team team = teamManager.getPlayerTeam(player.getUniqueId());
        if (team == null) {
            player.sendMessage(ChatColor.RED + "你不在任何队伍中！");
            return;
        }

        if (!team.isLeader(player.getUniqueId())) {
            player.sendMessage(ChatColor.RED + "只有队长才能设置队伍密码！");
            return;
        }

        String password = args[1];
        if (password.length() < 3 || password.length() > 20) {
            player.sendMessage(ChatColor.RED + "密码长度必须在3-20个字符之间！");
            return;
        }

        team.setPassword(password);
        player.sendMessage(ChatColor.GREEN + "队伍密码已设置！");
        player.sendMessage(ChatColor.GRAY + "提示: 使用 /team status password 来启用密码保护");
    }

    /**
     * 处理设备限制指令
     */
    private void handleDeviceRestriction(Player player, String[] args) {
        Team team = teamManager.getPlayerTeam(player.getUniqueId());
        if (team == null) {
            player.sendMessage(org.bukkit.ChatColor.RED + "你不在任何队伍中！");
            return;
        }

        if (!team.isLeader(player.getUniqueId())) {
            player.sendMessage(org.bukkit.ChatColor.RED + "只有队长才能设置设备限制！");
            return;
        }

        if (args.length < 2) {
            // 显示当前设备限制状态
            showCurrentDeviceRestriction(player, team);
            return;
        }

        String restrictionType = args[1].toLowerCase();
        TeamDeviceRestriction newRestriction;

        switch (restrictionType) {
            case "mixed":
            case "混合":
                newRestriction = TeamDeviceRestriction.MIXED;
                break;
            case "touch":
            case "触屏":
                newRestriction = TeamDeviceRestriction.TOUCHSCREEN_ONLY;
                break;
            case "nontouch":
            case "非触屏":
                newRestriction = TeamDeviceRestriction.NON_TOUCHSCREEN_ONLY;
                break;
            default:
                player.sendMessage(org.bukkit.ChatColor.RED + "无效的限制类型！");
                player.sendMessage(org.bukkit.ChatColor.YELLOW + "可用选项: mixed(混合), touch(仅触屏), nontouch(仅非触屏)");
                return;
        }

        team.setDeviceRestriction(newRestriction);
        player.sendMessage(org.bukkit.ChatColor.GREEN + "设备限制已设置为: " + org.bukkit.ChatColor.BOLD + newRestriction.getDisplayName());
        player.sendMessage(org.bukkit.ChatColor.GRAY + newRestriction.getDescription());

        // 通知队员限制变更
        notifyTeamMembers(team, org.bukkit.ChatColor.BLUE + "队伍设备限制已更新为: " + newRestriction.getDisplayName(),
                         player.getUniqueId());
    }

    /**
     * 显示当前设备限制状态
     */
    private void showCurrentDeviceRestriction(Player player, Team team) {
        TeamDeviceRestriction currentRestriction = team.getDeviceRestriction();

        player.sendMessage(org.bukkit.ChatColor.BLUE + "=== 队伍设备限制状态 ===");
        player.sendMessage(org.bukkit.ChatColor.YELLOW + "当前限制: " + org.bukkit.ChatColor.WHITE + currentRestriction.getDisplayName());
        player.sendMessage(org.bukkit.ChatColor.GRAY + "描述: " + currentRestriction.getDescription());
        player.sendMessage("");
        player.sendMessage(org.bukkit.ChatColor.GREEN + "可用设置:");
        player.sendMessage(org.bukkit.ChatColor.WHITE + "• /team restrict none " + org.bukkit.ChatColor.GRAY + "- 无限制");
        player.sendMessage(org.bukkit.ChatColor.WHITE + "• /team restrict mobile " + org.bukkit.ChatColor.GRAY + "- 仅限手机触屏");
        player.sendMessage(org.bukkit.ChatColor.WHITE + "• /team restrict pc " + org.bukkit.ChatColor.GRAY + "- 仅限其他设备");
        player.sendMessage(org.bukkit.ChatColor.WHITE + "• /team restrict smart " + org.bukkit.ChatColor.GRAY + "- 智能匹配");
        player.sendMessage(org.bukkit.ChatColor.WHITE + "• /team restrict warn " + org.bukkit.ChatColor.GRAY + "- 仅警告模式");
    }

    /**
     * 处理设备统计指令
     */
    private void handleDeviceStats(Player player) {
        Team team = teamManager.getPlayerTeam(player.getUniqueId());
        if (team == null) {
            player.sendMessage(org.bukkit.ChatColor.RED + "你不在任何队伍中！");
            return;
        }

        TeamDeviceAnalyzer analyzer = new TeamDeviceAnalyzer(teamManager);
        TeamDeviceAnalyzer.DeviceComposition composition = analyzer.analyzeTeam(team);

        player.sendMessage(org.bukkit.ChatColor.BLUE + "=== 队伍设备统计 ===");
        player.sendMessage(org.bukkit.ChatColor.YELLOW + "队伍类型: " + composition.getTeamType().getDisplayNameWithEmoji());
        player.sendMessage(org.bukkit.ChatColor.WHITE + "组成情况: " + composition.getCompositionDescription());
        player.sendMessage("");

        if (!composition.getDeviceInfos().isEmpty()) {
            player.sendMessage(org.bukkit.ChatColor.GREEN + "成员设备详情:");
            for (mclmli.operationdiamondv2main.device.DeviceDetector.PlayerDeviceInfo deviceInfo : composition.getDeviceInfos()) {
                Player member = Bukkit.getPlayer(deviceInfo.getPlayerId());
                String memberName = member != null ? member.getName() : "未知玩家";
                String roleTag = "";

                if (team.isLeader(deviceInfo.getPlayerId())) {
                    roleTag = org.bukkit.ChatColor.GOLD + "[队长] ";
                }

                player.sendMessage(org.bukkit.ChatColor.WHITE + "• " + roleTag + org.bukkit.ChatColor.RESET + memberName +
                                 org.bukkit.ChatColor.GRAY + " - " + deviceInfo.getFullDescription());
            }
        }

        player.sendMessage("");
        player.sendMessage(org.bukkit.ChatColor.GRAY + "当前设备限制: " + team.getDeviceRestriction().getDisplayName());
    }

    /**
     * 获取设备限制的描述信息
     */
    private String getDeviceRestrictionMessage(TeamDeviceRestriction restriction) {
        switch (restriction) {
            case MIXED:
                return ChatColor.GREEN + "混合模式 - 所有设备都可加入，会发送警告";
            case TOUCHSCREEN_ONLY:
                return ChatColor.YELLOW + "📱 仅限手机触屏玩家";
            case NON_TOUCHSCREEN_ONLY:
                return ChatColor.AQUA + "🎮 仅限其他设备玩家";
            default:
                return ChatColor.GRAY + "未知限制";
        }
    }

    /**
     * 获取玩家名称
     */
    private String getPlayerName(UUID playerId) {
        Player player = Bukkit.getPlayer(playerId);
        if (player != null) {
            return player.getName();
        }
        return Bukkit.getOfflinePlayer(playerId).getName();
    }

    /**
     * 发送帮助信息
     */
    private void sendHelpMessage(Player player) {
        player.sendMessage(ChatColor.GOLD + "=== 组队指令帮助 ===");
        player.sendMessage(ChatColor.YELLOW + "/team create <名称>" + ChatColor.WHITE + " - 创建队伍");
        player.sendMessage(ChatColor.YELLOW + "/team join <队伍名/ID> [密码]" + ChatColor.WHITE + " - 加入队伍");
        player.sendMessage(ChatColor.YELLOW + "/team leave" + ChatColor.WHITE + " - 退出队伍");
        player.sendMessage(ChatColor.YELLOW + "/team kick <玩家>" + ChatColor.WHITE + " - 踢出成员");
        player.sendMessage(ChatColor.YELLOW + "/team invite <玩家>" + ChatColor.WHITE + " - 邀请玩家");
        player.sendMessage(ChatColor.YELLOW + "/team accept" + ChatColor.WHITE + " - 接受邀请");
        player.sendMessage(ChatColor.YELLOW + "/team deny" + ChatColor.WHITE + " - 拒绝邀请");
        player.sendMessage(ChatColor.YELLOW + "/team apply <队伍名/ID> [留言]" + ChatColor.WHITE + " - 申请加入");
        player.sendMessage(ChatColor.YELLOW + "/team approve <玩家>" + ChatColor.WHITE + " - 批准申请");
        player.sendMessage(ChatColor.YELLOW + "/team transfer <玩家>" + ChatColor.WHITE + " - 转移队长");
        player.sendMessage(ChatColor.YELLOW + "/team status <状态>" + ChatColor.WHITE + " - 设置队伍状态");
        player.sendMessage(ChatColor.YELLOW + "/team info" + ChatColor.WHITE + " - 查看队伍信息");
        player.sendMessage(ChatColor.YELLOW + "/team password <密码>" + ChatColor.WHITE + " - 设置队伍密码");
        player.sendMessage(ChatColor.YELLOW + "/team devices" + ChatColor.WHITE + " - 查看设备统计");
        player.sendMessage(ChatColor.YELLOW + "/team restrict <none/mobile/nomobile>" + ChatColor.WHITE + " - 设置设备限制");
    }

    /**
     * 通知队伍成员（排除指定玩家）
     */
    private void notifyTeamMembers(Team team, String message, UUID... excludePlayerIds) {
        List<UUID> excludeList = Arrays.asList(excludePlayerIds);
        for (UUID memberId : team.getMembers()) {
            if (!excludeList.contains(memberId)) {
                Player member = Bukkit.getPlayer(memberId);
                if (member != null && member.isOnline()) {
                    member.sendMessage(message);
                }
            }
        }
    }

    /**
     * 通知队伍管理员（只有队长）
     */
    private void notifyTeamModerators(Team team, String message) {
        // 只通知队长
        Player leader = Bukkit.getPlayer(team.getLeaderId());
        if (leader != null && leader.isOnline()) {
            leader.sendMessage(message);
        }
    }

    /**
     * 播放命令反馈音效
     */
    private void playCommandSound(Player player, boolean success) {
        try {
            if (success) {
                // 成功音效 - 使用经验获得音效
                player.playSound(player.getLocation(), Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 0.7f, 1.2f);
            } else {
                // 失败音效 - 使用村民拒绝音效
                player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 0.7f, 0.8f);
            }
        } catch (Exception e) {
            // 兼容性处理，如果音效不存在则忽略
        }
    }

    /**
     * 播放特殊事件音效
     */
    private void playSpecialSound(Player player, String eventType) {
        try {
            switch (eventType.toLowerCase()) {
                case "join":
                    // 加入队伍音效
                    player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 0.5f, 1.5f);
                    break;
                case "leave":
                    // 离开队伍音效
                    player.playSound(player.getLocation(), Sound.ENTITY_ENDERMAN_TELEPORT, 0.5f, 0.8f);
                    break;
                case "invite":
                    // 邀请音效
                    player.playSound(player.getLocation(), Sound.BLOCK_NOTE_BLOCK_CHIME, 0.8f, 1.3f);
                    break;
                case "kick":
                    // 踢出音效
                    player.playSound(player.getLocation(), Sound.ENTITY_IRON_GOLEM_ATTACK, 0.6f, 0.7f);
                    break;
                case "transfer":
                    // 转移队长音效
                    player.playSound(player.getLocation(), Sound.ENTITY_ENDER_DRAGON_GROWL, 0.4f, 1.0f);
                    break;
                case "notification":
                    // 通知音效
                    player.playSound(player.getLocation(), Sound.BLOCK_NOTE_BLOCK_PLING, 0.6f, 1.0f);
                    break;
            }
        } catch (Exception e) {
            // 兼容性处理
        }
    }

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        if (!(sender instanceof Player)) {
            return new ArrayList<>();
        }

        Player player = (Player) sender;
        List<String> completions = new ArrayList<>();

        if (args.length == 1) {
            // 第一级命令补全 - 包含所有主要子命令和别名
            List<String> subCommands = Arrays.asList(
                "gui", "ui", "menu",                    // GUI相关
                "create", "c",                          // 创建队伍
                "join", "j",                           // 加入队伍
                "leave", "quit", "q",                  // 离开队伍
                "kick", "k",                           // 踢出成员
                "invite", "i",                         // 邀请玩家
                "accept", "a",                         // 接受邀请
                "deny", "reject", "d",                 // 拒绝邀请
                "apply",                               // 申请加入
                "approve",                             // 批准申请
                "transfer", "leader",                  // 转移队长
                "status", "set",                       // 设置状态
                "info", "list",                        // 查看信息
                "password", "pwd",                     // 设置密码
                "devices", "stats",                    // 设备统计
                "restrict",                            // 设备限制
                "help"                                 // 帮助
            );

            return subCommands.stream()
                .filter(cmd -> cmd.toLowerCase().startsWith(args[0].toLowerCase()))
                .sorted()
                .collect(Collectors.toList());
        }

        if (args.length == 2) {
            String subCommand = args[0].toLowerCase();
            String partial = args[1].toLowerCase();

            switch (subCommand) {
                case "status":
                case "set":
                    // 队伍状态选项
                    return Arrays.asList("open", "apply", "invite", "password")
                        .stream()
                        .filter(status -> status.startsWith(partial))
                        .collect(Collectors.toList());

                case "restrict":
                    // 设备限制选项
                    return Arrays.asList("mixed", "touch", "nontouch", "none", "mobile", "nomobile")
                        .stream()
                        .filter(restriction -> restriction.startsWith(partial))
                        .collect(Collectors.toList());

                case "join":
                case "j":
                case "apply":
                    // 提供可用队伍名称补全
                    completions.addAll(getAvailableTeamNames(player, partial));
                    return completions;

                case "kick":
                case "k":
                case "transfer":
                case "leader":
                    // 提供队伍成员名称补全（排除自己）
                    completions.addAll(getTeamMemberNames(player, partial, true));
                    return completions;

                case "invite":
                case "i":
                    // 提供不在队伍中的在线玩家名称补全
                    completions.addAll(getInvitablePlayerNames(player, partial));
                    return completions;

                case "approve":
                    // 提供有申请的玩家名称补全
                    completions.addAll(getApplicantNames(player, partial));
                    return completions;
            }
        }

        if (args.length == 3) {
            String subCommand = args[0].toLowerCase();
            String partial = args[2].toLowerCase();

            switch (subCommand) {
                case "join":
                case "j":
                    // 第三个参数可能是密码，不提供补全
                    return completions;

                case "apply":
                    // 第三个参数是申请留言，提供一些模板
                    return Arrays.asList("请让我加入", "希望能一起游戏", "我是新手请多指教")
                        .stream()
                        .filter(msg -> msg.startsWith(partial))
                        .collect(Collectors.toList());
            }
        }

        return completions;
    }

    /**
     * 获取可用的队伍名称列表（用于join和apply命令）
     */
    private List<String> getAvailableTeamNames(Player player, String partial) {
        List<String> teamNames = new ArrayList<>();

        // 获取所有队伍并过滤
        for (Team team : teamManager.getAllTeams()) {
            // 排除玩家自己的队伍
            if (teamManager.getPlayerTeam(player.getUniqueId()) != null &&
                teamManager.getPlayerTeam(player.getUniqueId()).getTeamId().equals(team.getTeamId())) {
                continue;
            }

            // 根据队伍状态过滤
            TeamStatus status = team.getStatus();
            if (status == TeamStatus.OPEN || status == TeamStatus.APPLY_REQUIRED ||
                status == TeamStatus.PASSWORD_REQUIRED) {

                String teamName = team.getTeamName();
                if (teamName.toLowerCase().startsWith(partial)) {
                    teamNames.add(teamName);
                }

                // 也添加队伍ID作为选项
                String teamId = team.getTeamId();
                if (teamId.toLowerCase().startsWith(partial)) {
                    teamNames.add(teamId);
                }
            }
        }

        return teamNames.stream().distinct().sorted().collect(Collectors.toList());
    }

    /**
     * 获取队伍成员名称列表
     */
    private List<String> getTeamMemberNames(Player player, String partial, boolean excludeSelf) {
        List<String> memberNames = new ArrayList<>();
        Team team = teamManager.getPlayerTeam(player.getUniqueId());

        if (team != null) {
            for (UUID memberId : team.getMembers()) {
                if (excludeSelf && memberId.equals(player.getUniqueId())) {
                    continue;
                }

                Player member = Bukkit.getPlayer(memberId);
                if (member != null && member.isOnline()) {
                    String memberName = member.getName();
                    if (memberName.toLowerCase().startsWith(partial)) {
                        memberNames.add(memberName);
                    }
                }
            }
        }

        return memberNames.stream().sorted().collect(Collectors.toList());
    }

    /**
     * 获取可邀请的玩家名称列表（不在任何队伍中的在线玩家）
     */
    private List<String> getInvitablePlayerNames(Player player, String partial) {
        List<String> playerNames = new ArrayList<>();

        for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
            // 排除自己
            if (onlinePlayer.getUniqueId().equals(player.getUniqueId())) {
                continue;
            }

            // 排除已在队伍中的玩家
            if (teamManager.getPlayerTeam(onlinePlayer.getUniqueId()) != null) {
                continue;
            }

            String playerName = onlinePlayer.getName();
            if (playerName.toLowerCase().startsWith(partial)) {
                playerNames.add(playerName);
            }
        }

        return playerNames.stream().sorted().collect(Collectors.toList());
    }

    /**
     * 获取有申请的玩家名称列表
     */
    private List<String> getApplicantNames(Player player, String partial) {
        List<String> applicantNames = new ArrayList<>();
        Team team = teamManager.getPlayerTeam(player.getUniqueId());

        if (team != null && team.isLeader(player.getUniqueId())) {
            // 遍历所有在线玩家，检查是否有申请
            for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
                if (onlinePlayer.getUniqueId().equals(player.getUniqueId())) {
                    continue; // 跳过自己
                }

                // 检查该玩家是否有申请加入当前队伍
                TeamApplication application = teamManager.getTeamApplication(team.getTeamId(), onlinePlayer.getUniqueId());
                if (application != null) {
                    String applicantName = onlinePlayer.getName();
                    if (applicantName.toLowerCase().startsWith(partial)) {
                        applicantNames.add(applicantName);
                    }
                }
            }
        }

        return applicantNames.stream().sorted().collect(Collectors.toList());
    }
}
