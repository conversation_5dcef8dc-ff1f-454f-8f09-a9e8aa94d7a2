package mclmli.operationdiamondv2main.team;

import mclmli.operationdiamondv2main.device.DeviceDetector;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

import java.util.*;

/**
 * 队伍设备组成分析器
 * 用于分析队伍中玩家的设备类型组成，判断队伍类型
 */
public class TeamDeviceAnalyzer {

    private final TeamManager teamManager;

    public TeamDeviceAnalyzer(TeamManager teamManager) {
        this.teamManager = teamManager;
    }

    /**
     * 分析队伍设备组成
     * @param team 要分析的队伍
     * @return 设备组成分析结果
     */
    public DeviceComposition analyzeTeam(Team team) {
        List<DeviceDetector.PlayerDeviceInfo> deviceInfos = new ArrayList<>();
        int touchscreenCount = 0;
        int nonTouchscreenCount = 0;

        // 分析每个队员的设备
        for (UUID memberId : team.getMembers()) {
            Player player = Bukkit.getPlayer(memberId);
            if (player != null && player.isOnline()) {
                DeviceDetector.PlayerDeviceInfo deviceInfo =
                    DeviceDetector.detectPlayerDevice(memberId, teamManager);

                if (deviceInfo != null) {
                    deviceInfos.add(deviceInfo);

                    // 判断是否为触屏玩家
                    if (deviceInfo.isTouchInput()) {
                        touchscreenCount++;
                    } else {
                        nonTouchscreenCount++;
                    }
                }
            }
        }

        // 确定队伍类型
        TeamDeviceType teamType;
        if (nonTouchscreenCount == 0 && touchscreenCount > 0) {
            // 只有触屏玩家
            teamType = TeamDeviceType.TOUCHSCREEN_ONLY;
        } else {
            // 包含非触屏玩家
            teamType = TeamDeviceType.MIXED_OR_OTHER;
        }

        return new DeviceComposition(teamType, touchscreenCount, nonTouchscreenCount, deviceInfos);
    }

    /**
     * 判断玩家是否为触屏玩家
     * @param deviceInfo 玩家设备信息
     * @return 如果是触屏玩家返回true
     */
    public static boolean isTouchscreenPlayer(DeviceDetector.PlayerDeviceInfo deviceInfo) {
        return deviceInfo.isTouchInput();
    }

    /**
     * 预测玩家加入后的队伍类型
     * @param team 队伍
     * @param newPlayerId 新加入的玩家ID
     * @return 加入后的队伍类型
     */
    public TeamDeviceType predictTeamTypeAfterJoin(Team team, UUID newPlayerId) {
        DeviceDetector.PlayerDeviceInfo newPlayerDevice =
            DeviceDetector.detectPlayerDevice(newPlayerId, teamManager);

        if (newPlayerDevice == null) {
            return TeamDeviceType.MIXED_OR_OTHER; // 无法检测时默认为其他队伍
        }

        DeviceComposition currentComposition = analyzeTeam(team);

        // 如果新玩家不是触屏玩家，队伍必然变为其他队伍
        if (!isTouchscreenPlayer(newPlayerDevice)) {
            return TeamDeviceType.MIXED_OR_OTHER;
        }

        // 如果新玩家是触屏玩家，保持当前队伍类型
        return currentComposition.getTeamType();
    }

    /**
     * 设备组成分析结果
     */
    public static class DeviceComposition {
        private final TeamDeviceType teamType;
        private final int touchscreenCount;
        private final int nonTouchscreenCount;
        private final List<DeviceDetector.PlayerDeviceInfo> deviceInfos;

        public DeviceComposition(TeamDeviceType teamType, int touchscreenCount,
                               int nonTouchscreenCount, List<DeviceDetector.PlayerDeviceInfo> deviceInfos) {
            this.teamType = teamType;
            this.touchscreenCount = touchscreenCount;
            this.nonTouchscreenCount = nonTouchscreenCount;
            this.deviceInfos = new ArrayList<>(deviceInfos);
        }

        public TeamDeviceType getTeamType() {
            return teamType;
        }

        public int getTouchscreenCount() {
            return touchscreenCount;
        }

        public int getNonTouchscreenCount() {
            return nonTouchscreenCount;
        }

        public int getTotalCount() {
            return touchscreenCount + nonTouchscreenCount;
        }

        public List<DeviceDetector.PlayerDeviceInfo> getDeviceInfos() {
            return new ArrayList<>(deviceInfos);
        }

        /**
         * 获取组成描述
         */
        public String getCompositionDescription() {
            if (teamType == TeamDeviceType.TOUCHSCREEN_ONLY) {
                return String.format("👆 仅触屏队伍 (%d人)", touchscreenCount);
            } else {
                return String.format("⌨️ 混合设备队伍 (触屏:%d人, 非触屏:%d人)",
                    touchscreenCount, nonTouchscreenCount);
            }
        }

        /**
         * 获取详细设备列表
         */
        public List<String> getDetailedDeviceList() {
            List<String> details = new ArrayList<>();
            for (DeviceDetector.PlayerDeviceInfo info : deviceInfos) {
                Player player = Bukkit.getPlayer(info.getPlayerId());
                if (player != null) {
                    String deviceDesc = info.getShortDescription();
                    String playerType = info.isTouchInput() ? "👆" : "⌨️";
                    details.add(String.format("%s %s: %s", playerType, player.getName(), deviceDesc));
                }
            }
            return details;
        }
    }
}
