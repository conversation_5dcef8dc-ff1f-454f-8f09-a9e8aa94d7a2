package mclmli.operationdiamondv2main.team;

/**
 * 队伍设备限制枚举
 * 定义队伍允许加入的设备类型限制
 */
public enum TeamDeviceRestriction {
    /**
     * 混合模式（默认） - 所有设备类型都可以加入，但会发送设备差异警告
     */
    MIXED("混合模式", "所有设备类型的玩家都可以加入，会提醒设备类型差异"),

    /**
     * 仅限触屏 - 只允许触屏玩家加入
     */
    TOUCHSCREEN_ONLY("仅限触屏", "只允许触屏玩家加入队伍"),

    /**
     * 仅限非触屏 - 不允许触屏玩家加入
     */
    NON_TOUCHSCREEN_ONLY("仅限非触屏", "不允许触屏玩家加入队伍");

    private final String displayName;
    private final String description;

    TeamDeviceRestriction(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 获取带描述的完整显示
     */
    public String getFullDescription() {
        return displayName + " - " + description;
    }

    /**
     * 判断是否需要设备验证
     */
    public boolean requiresDeviceValidation() {
        return this != MIXED;
    }

    /**
     * 判断是否允许发送警告
     */
    public boolean allowsWarnings() {
        return this == MIXED;
    }

    /**
     * 获取默认的设备限制模式
     */
    public static TeamDeviceRestriction getDefault() {
        return MIXED;
    }
}
