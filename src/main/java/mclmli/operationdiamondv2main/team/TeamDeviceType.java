package mclmli.operationdiamondv2main.team;

/**
 * 队伍设备类型枚举
 * 基于队伍成员的设备组成来分类队伍类型
 */
public enum TeamDeviceType {
    /**
     * 仅触屏玩家队伍
     * 队伍中所有玩家都是使用触屏操作的玩家
     */
    TOUCHSCREEN_ONLY("仅触屏", "👆", "队伍中只有触屏玩家"),

    /**
     * 其他队伍
     * 队伍中包含至少一个非触屏玩家
     */
    MIXED_OR_OTHER("混合设备", "⌨️", "队伍中包含非触屏玩家");

    private final String displayName;
    private final String emoji;
    private final String description;

    TeamDeviceType(String displayName, String emoji, String description) {
        this.displayName = displayName;
        this.emoji = emoji;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getEmoji() {
        return emoji;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 获取带图标的显示名称
     */
    public String getDisplayNameWithEmoji() {
        return emoji + " " + displayName;
    }
}
