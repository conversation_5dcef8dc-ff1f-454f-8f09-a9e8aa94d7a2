package mclmli.operationdiamondv2main.team;

import mclmli.operationdiamondv2main.OperationDiamondv2_Main;
import mclmli.operationdiamondv2main.device.DeviceManager;
import mclmli.operationdiamondv2main.device.DeviceDetector;
import org.bukkit.entity.Player;
import org.geysermc.floodgate.api.FloodgateApi;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 简化版队伍表单管理器 - 为基岩版玩家提供基本的表单界面
 * 使用精美的聊天界面代替复杂的表单API
 */
public class TeamFormManager {

    private final OperationDiamondv2_Main plugin;
    private final TeamManager teamManager;
    private final DeviceManager deviceManager;
    
    // 表单状态管理
    private final Map<UUID, String> playerFormStates = new ConcurrentHashMap<>();

    public TeamFormManager(OperationDiamondv2_Main plugin, TeamManager teamManager, DeviceManager deviceManager) {
        this.plugin = plugin;
        this.teamManager = teamManager;
        this.deviceManager = deviceManager;
    }

    /**
     * 检查玩家是否为基岩版
     */
    public boolean isBedrockPlayer(Player player) {
        try {
            FloodgateApi api = FloodgateApi.getInstance();
            return api.isFloodgatePlayer(player.getUniqueId());
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 打开主菜单 - 使用精美的聊天界面
     */
    public void openMainMenu(Player player) {
        if (!isBedrockPlayer(player)) {
            return;
        }

        Team playerTeam = teamManager.getPlayerTeam(player.getUniqueId());
        
        // 发送精美的标题
        player.sendMessage("");
        player.sendMessage("§6§l✦═══════════════════════✦");
        player.sendMessage("§6§l        队伍管理中心        ");
        player.sendMessage("§6§l✦═══════════════════════✦");
        player.sendMessage("");

        if (playerTeam == null) {
            // 无队伍状态
            player.sendMessage("§e🎯 §l可用操作:");
            player.sendMessage("");
            player.sendMessage("§a⚡ §f输入 §b/team create §f创建队伍");
            player.sendMessage("§b🔍 §f输入 §b/team list §f浏览队伍");
            player.sendMessage("§d📧 §f输入 §b/team invites §f查看邀请");
            
            // 检查邀请
            TeamInvitation invitation = teamManager.getPlayerInvitation(player.getUniqueId());
            if (invitation != null) {
                Team inviteTeam = teamManager.getTeam(invitation.getTeamId());
                if (inviteTeam != null) {
                    player.sendMessage("");
                    player.sendMessage("§d§l📧 你有一个队伍邀请!");
                    player.sendMessage("§7队伍: §6" + inviteTeam.getTeamName());
                    player.sendMessage("§7队长: §a" + getPlayerName(inviteTeam.getLeaderId()));
                    player.sendMessage("§a✅ §f输入 §b/team accept §f接受邀请");
                    player.sendMessage("§c❌ §f输入 §b/team deny §f拒绝邀请");
                }
            }
            
            if (player.hasPermission("operationdiamondv2.admin")) {
                player.sendMessage("§c📊 §f输入 §b/team stats §f查看设备统计 §7(管理员)");
            }
        } else {
            // 有队伍状态
            boolean isLeader = playerTeam.isLeader(player.getUniqueId());
            
            player.sendMessage("§6§l当前队伍: §f" + playerTeam.getTeamName());
            player.sendMessage("§7成员: §b" + playerTeam.getMemberCount() + "§7/§b" + playerTeam.getMaxMembers());
            player.sendMessage("§7状态: " + getTeamStatusDisplay(playerTeam.getStatus()));
            player.sendMessage("");
            
            player.sendMessage("§e🎯 §l可用操作:");
            player.sendMessage("");
            player.sendMessage("§bℹ️ §f输入 §b/team info §f查看队伍详情");
            player.sendMessage("§b👥 §f输入 §b/team members §f查看成员列表");
            player.sendMessage("§c📊 §f输入 §b/team stats §f查看设备统计");
            
            if (isLeader) {
                player.sendMessage("");
                player.sendMessage("§6§l队长专用:");
                player.sendMessage("§a📧 §f输入 §b/team invite <玩家> §f邀请玩家");
                player.sendMessage("§e⚙️ §f输入 §b/team settings §f队伍设置");
                player.sendMessage("§d📝 §f输入 §b/team applications §f处理申请");
                player.sendMessage("§c💥 §f输入 §b/team disband §f解散队伍");
            } else {
                player.sendMessage("§c🚪 §f输入 §b/team leave §f离开队伍");
            }
        }
        
        player.sendMessage("");
        player.sendMessage("§e📖 §f输入 §b/team help §f查看详细帮助");
        player.sendMessage("§6§l✦═══════════════════════✦");
        player.sendMessage("");
    }

    /**
     * 显示队伍列表
     */
    public void showTeamList(Player player) {
        if (!isBedrockPlayer(player)) {
            return;
        }

        List<Team> publicTeams = teamManager.getPublicTeams();
        
        player.sendMessage("");
        player.sendMessage("§6§l🔍═══════ 公开队伍列表 ═══════🔍");
        player.sendMessage("");
        
        if (publicTeams.isEmpty()) {
            player.sendMessage("§e当前没有公开的队伍");
            player.sendMessage("§7你可以创建一个新队伍: §b/team create <名称>");
            player.sendMessage("");
            player.sendMessage("§6§l═══════════════════════════════");
            return;
        }

        for (int i = 0; i < publicTeams.size() && i < 10; i++) {
            Team team = publicTeams.get(i);
            player.sendMessage(String.format("§6%d. §f%s §7(%d/%d) %s", 
                i + 1,
                team.getTeamName(),
                team.getMemberCount(),
                team.getMaxMembers(),
                getTeamStatusDisplay(team.getStatus())));
            player.sendMessage("   §7队长: §a" + getPlayerName(team.getLeaderId()));
            
            // 显示加入提示
            switch (team.getStatus()) {
                case OPEN:
                    player.sendMessage("   §a▶ §f输入 §b/team join " + team.getTeamName() + " §a直接加入");
                    break;
                case PASSWORD_REQUIRED:
                    player.sendMessage("   §e▶ §f输入 §b/team join " + team.getTeamName() + " <密码> §e加入");
                    break;
                case APPLY_REQUIRED:
                    player.sendMessage("   §b▶ §f输入 §b/team apply " + team.getTeamName() + " §b申请加入");
                    break;
                case INVITE_ONLY:
                    player.sendMessage("   §c▶ 仅限邀请加入");
                    break;
            }
            player.sendMessage("");
        }
        
        if (publicTeams.size() > 10) {
            player.sendMessage("§7... 还有 " + (publicTeams.size() - 10) + " 个队伍");
            player.sendMessage("");
        }
        
        player.sendMessage("§6§l═══════════════════════════════");
    }

    /**
     * 显示队伍详细信息
     */
    public void showTeamInfo(Player player, Team team) {
        if (!isBedrockPlayer(player)) {
            return;
        }

        player.sendMessage("");
        player.sendMessage("§6§l✦═══════ " + team.getTeamName() + " ═══════✦");
        player.sendMessage("");
        player.sendMessage("§7队伍ID: §f" + team.getTeamId());
        player.sendMessage("§7队长: §a" + getPlayerName(team.getLeaderId()));
        player.sendMessage("§7成员数量: §b" + team.getMemberCount() + "§7/§b" + team.getMaxMembers());
        player.sendMessage("§7队伍状态: " + getTeamStatusDisplay(team.getStatus()));
        player.sendMessage("§7设备限制: " + getDeviceRestrictionDisplay(team.getDeviceRestriction()));
        player.sendMessage("§7创建时间: §f" + team.getCreateTime().toString().substring(0, 19).replace('T', ' '));
        player.sendMessage("");
        
        player.sendMessage("§e👥 成员列表:");
        for (UUID memberId : team.getMembers()) {
            String memberName = getPlayerName(memberId);
            String role = team.isLeader(memberId) ? " §6[队长]" : "";
            org.bukkit.entity.Player member = org.bukkit.Bukkit.getPlayer(memberId);
            String onlineStatus = (member != null && member.isOnline()) ? " §a[在线]" : " §7[离线]";
            
            player.sendMessage("§f• " + memberName + role + onlineStatus);
        }
        player.sendMessage("");
        player.sendMessage("§6§l✦═══════════════════════✦");
    }

    /**
     * 显示成员列表
     */
    public void showMemberList(Player player, Team team) {
        if (!isBedrockPlayer(player)) {
            return;
        }

        boolean isLeader = team.isLeader(player.getUniqueId());
        
        player.sendMessage("");
        player.sendMessage("§6§l👥═══════ 队伍成员 ═══════👥");
        player.sendMessage("§7队伍: §6" + team.getTeamName());
        player.sendMessage("§7成员数量: §b" + team.getMemberCount() + "§7/§b" + team.getMaxMembers());
        player.sendMessage("");
        
        for (UUID memberId : team.getMembers()) {
            String memberName = getPlayerName(memberId);
            String role = team.isLeader(memberId) ? " §6[队长]" : "";
            org.bukkit.entity.Player member = org.bukkit.Bukkit.getPlayer(memberId);
            String onlineStatus = (member != null && member.isOnline()) ? " §a[在线]" : " §7[离线]";
            
            // 获取设备信息
            DeviceDetector.PlayerDeviceInfo deviceInfo = deviceManager.getPlayerDeviceInfo(memberId);
            String deviceDisplay = deviceInfo != null ? " §8(" + deviceInfo.getShortDescription() + ")" : "";
            
            player.sendMessage("§f• " + memberName + role + onlineStatus + deviceDisplay);
            
            // 队长可以踢出成员
            if (isLeader && !team.isLeader(memberId) && !memberId.equals(player.getUniqueId())) {
                player.sendMessage("  §c▶ §f输入 §b/team kick " + memberName + " §c踢出成员");
            }
        }
        
        if (isLeader && team.getMemberCount() < team.getMaxMembers()) {
            player.sendMessage("");
            player.sendMessage("§a📧 §f输入 §b/team invite <玩家> §a邀请新成员");
        }
        
        player.sendMessage("");
        player.sendMessage("§6§l═══════════════════════════");
    }

    /**
     * 获取玩家名称
     */
    private String getPlayerName(UUID playerId) {
        org.bukkit.entity.Player player = org.bukkit.Bukkit.getPlayer(playerId);
        return player != null ? player.getName() : "未知玩家";
    }

    /**
     * 获取队伍状态显示
     */
    private String getTeamStatusDisplay(TeamStatus status) {
        return switch (status) {
            case OPEN -> "§a公开";
            case PASSWORD_REQUIRED -> "§e密码保护";
            case INVITE_ONLY -> "§c仅限邀请";
            case APPLY_REQUIRED -> "§b需要申请";
            default -> "§7未知";
        };
    }
    
    /**
     * 获取设备限制显示
     */
    private String getDeviceRestrictionDisplay(TeamDeviceRestriction restriction) {
        return switch (restriction) {
            case MIXED -> "§f混合设备";
            case NON_TOUCHSCREEN_ONLY -> "§b非触屏专用";
            case TOUCHSCREEN_ONLY -> "§a触屏专用";
            default -> "§7未知";
        };
    }
}
