package mclmli.operationdiamondv2main.team;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 队伍邀请数据类
 * 处理队伍邀请相关信息
 */
public class TeamInvitation {

    private final String invitationId;
    private final String teamId;
    private final UUID inviterId; // 邀请者
    private final UUID inviteeId; // 被邀请者
    private final LocalDateTime inviteTime;
    private final LocalDateTime expireTime;
    private boolean expired;

    /**
     * 创建队伍邀请
     * @param teamId 队伍ID
     * @param inviterId 邀请者UUID
     * @param inviteeId 被邀请者UUID
     */
    public TeamInvitation(String teamId, UUID inviterId, UUID inviteeId) {
        this.invitationId = UUID.randomUUID().toString();
        this.teamId = teamId;
        this.inviterId = inviterId;
        this.inviteeId = inviteeId;
        this.inviteTime = LocalDateTime.now();
        this.expireTime = inviteTime.plusMinutes(1); // 修改为1分钟后过期
        this.expired = false;
    }

    public String getInvitationId() {
        return invitationId;
    }

    public String getTeamId() {
        return teamId;
    }

    public UUID getInviterId() {
        return inviterId;
    }

    public UUID getInviteeId() {
        return inviteeId;
    }

    public LocalDateTime getInviteTime() {
        return inviteTime;
    }

    public LocalDateTime getExpireTime() {
        return expireTime;
    }

    /**
     * 检查邀请是否过期
     * @return 是否过期
     */
    public boolean isExpired() {
        if (!expired && LocalDateTime.now().isAfter(expireTime)) {
            expired = true;
        }
        return expired;
    }

    /**
     * 检查邀请是否有效
     * @return 是否有效
     */
    public boolean isValid() {
        return !isExpired();
    }

    /**
     * 手动设置邀请为过期
     */
    public void markExpired() {
        this.expired = true;
    }

    @Override
    public String toString() {
        return String.format("TeamInvitation{id='%s', teamId='%s', inviter=%s, invitee=%s, expired=%b}",
            invitationId, teamId, inviterId, inviteeId, expired);
    }
}
