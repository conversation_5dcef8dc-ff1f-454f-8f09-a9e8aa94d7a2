package mclmli.operationdiamondv2main.team;

import mclmli.operationdiamondv2main.device.DeviceDetector;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;

import java.util.UUID;

/**
 * 队伍加入验证和警告系统
 * 处理玩家加入队伍时的设备类型验证和警告消息
 */
public class TeamJoinValidator {

    private final TeamManager teamManager;
    private final TeamDeviceAnalyzer deviceAnalyzer;

    public TeamJoinValidator(TeamManager teamManager) {
        this.teamManager = teamManager;
        this.deviceAnalyzer = new TeamDeviceAnalyzer(teamManager);
    }

    /**
     * 验证玩家是否可以加入队伍，并发送相应警告
     * @param team 目标队伍
     * @param playerId 要加入的玩家ID
     * @return 验证结果
     */
    public JoinValidationResult validateAndWarn(Team team, UUID playerId) {
        Player joiningPlayer = Bukkit.getPlayer(playerId);
        if (joiningPlayer == null) {
            return new JoinValidationResult(false, "玩家不在线");
        }

        // 检测加入玩家的设备信息
        DeviceDetector.PlayerDeviceInfo joiningPlayerDevice =
            DeviceDetector.detectPlayerDevice(playerId, teamManager);

        if (joiningPlayerDevice == null) {
            return new JoinValidationResult(false, "无法检测玩家设备信息");
        }

        // 分析当前队伍组成
        TeamDeviceAnalyzer.DeviceComposition currentComposition = deviceAnalyzer.analyzeTeam(team);
        boolean isJoiningPlayerTouchscreen = TeamDeviceAnalyzer.isTouchscreenPlayer(joiningPlayerDevice);

        // 预测加入后的队伍类型
        TeamDeviceType predictedType = deviceAnalyzer.predictTeamTypeAfterJoin(team, playerId);

        // 发送警告消息
        sendWarningMessages(team, joiningPlayer, currentComposition, isJoiningPlayerTouchscreen, predictedType);

        return new JoinValidationResult(true, "加入成功");
    }

    /**
     * 发送警告消息给相关玩家
     */
    private void sendWarningMessages(Team team, Player joiningPlayer,
                                   TeamDeviceAnalyzer.DeviceComposition currentComposition,
                                   boolean isJoiningPlayerTouchscreen,
                                   TeamDeviceType predictedType) {

        // 情况1：触屏玩家加入其他队伍
        if (isJoiningPlayerTouchscreen && currentComposition.getTeamType() == TeamDeviceType.MIXED_OR_OTHER) {
            sendTouchscreenJoiningOtherTeamWarnings(team, joiningPlayer);
        }
        // 情况2：其他玩家加入仅触屏队伍
        else if (!isJoiningPlayerTouchscreen && currentComposition.getTeamType() == TeamDeviceType.TOUCHSCREEN_ONLY) {
            sendOtherPlayerJoiningTouchscreenTeamWarnings(team, joiningPlayer);
        }
        // 情况3：队伍类型发生变化的其他情况
        else if (currentComposition.getTeamType() != predictedType) {
            sendTeamTypeChangeWarnings(team, joiningPlayer, currentComposition.getTeamType(), predictedType);
        }
    }

    /**
     * 触屏玩家加入其他队伍的警告
     */
    private void sendTouchscreenJoiningOtherTeamWarnings(Team team, Player joiningPlayer) {
        // 向加入的触屏玩家发送警告
        joiningPlayer.sendMessage(ChatColor.YELLOW + "⚠️ 警告：你正在加入包含非触屏玩家的队伍");
        joiningPlayer.sendMessage(ChatColor.YELLOW + "👆 作为触屏玩家，你将与非触屏玩家一起匹配");
        joiningPlayer.sendMessage(ChatColor.GRAY + "💡 建议：考虑加入仅触屏玩家的队伍以获得更好的游戏体验");

        // 向队伍中的其他玩家发送警告
        String warningMessage = ChatColor.YELLOW + "⚠️ 队伍提醒：" + ChatColor.GOLD + joiningPlayer.getName() +
                               ChatColor.YELLOW + " (触屏玩家) 加入了队伍";
        String tipMessage = ChatColor.GRAY + "💡 提示：触屏玩家可能在操作上处于劣势，请多加照顾";

        for (UUID memberId : team.getMembers()) {
            Player member = Bukkit.getPlayer(memberId);
            if (member != null && !member.equals(joiningPlayer)) {
                member.sendMessage(warningMessage);
                member.sendMessage(tipMessage);
            }
        }
    }

    /**
     * 其他玩家加入仅触屏队伍的警告
     */
    private void sendOtherPlayerJoiningTouchscreenTeamWarnings(Team team, Player joiningPlayer) {
        // 向队伍中的触屏玩家发送警告
        String warningMessage = ChatColor.YELLOW + "⚠️ 队伍提醒：" + ChatColor.GOLD + joiningPlayer.getName() +
                               ChatColor.YELLOW + " (非触屏玩家) 加入了队伍";
        String changeMessage = ChatColor.YELLOW + "👆➡️⌨️ 队伍类型已从 \"仅触屏\" 变更为 \"混合设备\"";

        for (UUID memberId : team.getMembers()) {
            Player member = Bukkit.getPlayer(memberId);
            if (member != null && !member.equals(joiningPlayer)) {
                DeviceDetector.PlayerDeviceInfo memberDevice =
                    DeviceDetector.detectPlayerDevice(memberId, teamManager);

                if (memberDevice != null && TeamDeviceAnalyzer.isTouchscreenPlayer(memberDevice)) {
                    member.sendMessage(warningMessage);
                    member.sendMessage(changeMessage);
                    member.sendMessage(ChatColor.GRAY + "💡 提示：队伍现在将与混合设备队伍匹配");
                }
            }
        }

        // 向加入的非触屏玩家发送提醒
        joiningPlayer.sendMessage(ChatColor.YELLOW + "⚠️ 提醒：你加入了原本的仅触屏队伍");
        joiningPlayer.sendMessage(ChatColor.YELLOW + "👆 队伍中的触屏玩家可能在操作上处于劣势");
        joiningPlayer.sendMessage(ChatColor.GRAY + "💡 建议：在游戏中多加照顾队友");
    }

    /**
     * 队伍类型变化的通用警告
     */
    private void sendTeamTypeChangeWarnings(Team team, Player joiningPlayer,
                                          TeamDeviceType oldType, TeamDeviceType newType) {
        String changeMessage = ChatColor.YELLOW + "📋 队伍类型变更：" +
                              oldType.getDisplayNameWithEmoji() + " ➡️ " + newType.getDisplayNameWithEmoji();

        // 发送给所有队员
        for (UUID memberId : team.getMembers()) {
            Player member = Bukkit.getPlayer(memberId);
            if (member != null) {
                member.sendMessage(changeMessage);
            }
        }

        // 发送给加入的玩家
        joiningPlayer.sendMessage(changeMessage);
    }

    /**
     * 加入验证结果类
     */
    public static class JoinValidationResult {
        private final boolean allowed;
        private final String message;

        public JoinValidationResult(boolean allowed, String message) {
            this.allowed = allowed;
            this.message = message;
        }

        public boolean isAllowed() {
            return allowed;
        }

        public String getMessage() {
            return message;
        }
    }
}
