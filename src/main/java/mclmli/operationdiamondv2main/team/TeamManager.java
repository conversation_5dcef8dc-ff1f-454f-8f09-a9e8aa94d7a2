package mclmli.operationdiamondv2main.team;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.geysermc.geyser.api.GeyserApi;
import org.geysermc.geyser.api.connection.GeyserConnection;
import org.geysermc.floodgate.api.FloodgateApi;
import org.geysermc.floodgate.api.player.FloodgatePlayer;
import mclmli.operationdiamondv2main.device.DeviceDetector;

import java.util.UUID;
import java.util.logging.Logger;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;

/**
 * 组队管理器 - 负责处理所有组队相关的逻辑
 * 支持Java版和基岩版玩家的跨平台组队功能
 * 集成Geyser和Floodgate API提供完整的跨平台支持
 */
public class TeamManager {

    private static final Logger LOGGER = Bukkit.getLogger();
    private boolean geyserEnabled = false;
    private boolean floodgateEnabled = false;

    // 设备相关组件
    private TeamDeviceAnalyzer deviceAnalyzer;
    private TeamJoinValidator joinValidator;

    // 数据存储
    private final Map<String, Team> teams = new ConcurrentHashMap<>(); // teamId -> Team
    private final Map<String, Team> teamsByName = new ConcurrentHashMap<>(); // teamName -> Team
    private final Map<UUID, String> playerTeams = new ConcurrentHashMap<>(); // playerId -> teamId
    private final Map<String, TeamInvitation> invitations = new ConcurrentHashMap<>(); // invitationId -> TeamInvitation
    private final Map<UUID, String> playerInvitations = new ConcurrentHashMap<>(); // playerId -> invitationId
    private final Map<String, TeamApplication> applications = new ConcurrentHashMap<>(); // applicationId -> TeamApplication
    private final Map<String, List<String>> teamApplications = new ConcurrentHashMap<>(); // teamId -> List<applicationId>

    /**
     * 加入队伍的结果枚举
     */
    public enum JoinResult {
        SUCCESS,
        TEAM_FULL,
        WRONG_PASSWORD,
        INVITE_ONLY,
        APPLY_REQUIRED,
        ALREADY_MEMBER,
        TEAM_NOT_FOUND,
        DEVICE_NOT_ALLOWED
    }

    /**
     * 初始化组队管理器
     */
    public void initialize() {
        // 检测Geyser是否可用
        try {
            GeyserApi.api();
            geyserEnabled = true;
            LOGGER.info("Geyser API 已启用，支持基岩版玩家组队功能");
        } catch (Exception e) {
            geyserEnabled = false;
            LOGGER.info("Geyser API 未找到");
        }

        // 检测Floodgate是否可用
        try {
            FloodgateApi.getInstance();
            floodgateEnabled = true;
            LOGGER.info("Floodgate API 已启用，支持增强的基岩版玩家功能");
        } catch (Exception e) {
            floodgateEnabled = false;
            LOGGER.info("Floodgate API 未找到");
        }

        // 初始化设备相关组件
        this.deviceAnalyzer = new TeamDeviceAnalyzer(this);
        this.joinValidator = new TeamJoinValidator(this);

        // 输出综合状态
        if (geyserEnabled && floodgateEnabled) {
            LOGGER.info("完整的跨平台支持已启用 (Geyser + Floodgate)");
        } else if (geyserEnabled || floodgateEnabled) {
            LOGGER.info("部分跨平台支持已启用");
        } else {
            LOGGER.info("仅支持Java版组队功能");
        }
    }

    /**
     * 检查玩家是否为基岩版玩家
     * 优先使用Floodgate API，如果不可用则使用Geyser API
     * @param player 要检查的玩家
     * @return 如果是基岩版玩家返回true，否则返回false
     */
    public boolean isBedrockPlayer(Player player) {
        return isBedrockPlayer(player.getUniqueId());
    }

    /**
     * 检查玩家是否为基岩版玩家
     * 优先使用Floodgate API，如果不可用则使用Geyser API
     * @param playerId 玩家UUID
     * @return 如果是基岩版玩家返回true，否则返回false
     */
    public boolean isBedrockPlayer(UUID playerId) {
        // 优先使用Floodgate API
        if (floodgateEnabled) {
            try {
                return FloodgateApi.getInstance().isFloodgatePlayer(playerId);
            } catch (Exception e) {
                LOGGER.warning("使用Floodgate检查基岩版玩家状态时发生错误: " + e.getMessage());
            }
        }

        // 如果Floodgate不可用，使用Geyser API
        if (geyserEnabled) {
            try {
                return GeyserApi.api().isBedrockPlayer(playerId);
            } catch (Exception e) {
                LOGGER.warning("使用Geyser检查基岩版玩家状态时发生错误: " + e.getMessage());
            }
        }

        return false;
    }

    /**
     * 获取基岩版玩家的Floodgate信息
     * @param playerId 玩家UUID
     * @return FloodgatePlayer实例，如果不是基岩版玩家或Floodgate不可用则返回null
     */
    public FloodgatePlayer getFloodgatePlayer(UUID playerId) {
        if (!floodgateEnabled) {
            return null;
        }

        try {
            return FloodgateApi.getInstance().getPlayer(playerId);
        } catch (Exception e) {
            LOGGER.warning("获取Floodgate玩家信息时发生错误: " + e.getMessage());
            return null;
        }
    }

    /**
     * 获取基岩版玩家的连接信息
     * @param playerId 玩家UUID
     * @return 基岩版玩家连接，如果不是基岩版玩家则返回null
     */
    public GeyserConnection getBedrockConnection(UUID playerId) {
        if (!geyserEnabled) {
            return null;
        }

        try {
            return GeyserApi.api().connectionByUuid(playerId);
        } catch (Exception e) {
            LOGGER.warning("获取基岩版玩家连接时发生错误: " + e.getMessage());
            return null;
        }
    }

    /**
     * 获取在线基岩版玩家数量
     * @return 在线基岩版玩家数量
     */
    public int getOnlineBedrockPlayerCount() {
        if (!geyserEnabled) {
            return 0;
        }

        try {
            return GeyserApi.api().onlineConnectionsCount();
        } catch (Exception e) {
            LOGGER.warning("获取在线基岩版玩家数量时发生错误: " + e.getMessage());
            return 0;
        }
    }

    /**
     * 获取基岩版玩家的详细信息
     * @param playerId 玩家UUID
     * @return 包含平台、设备类型等信息的字符串，如果不是基岩版玩家则返回"Java版"
     */
    public String getPlayerPlatformInfo(UUID playerId) {
        if (isBedrockPlayer(playerId)) {
            // 尝试获取更详细的设备信息
            DeviceDetector.PlayerDeviceInfo deviceInfo = DeviceDetector.detectPlayerDevice(playerId, this);
            if (deviceInfo != null) {
                return deviceInfo.getShortDescription();
            }
            return "基岩版";
        } else {
            return "Java版";
        }
    }

    /**
     * 获取玩家完整设备信息
     * @param playerId 玩家UUID
     * @return 完整设备信息字符串
     */
    public String getPlayerFullDeviceInfo(UUID playerId) {
        DeviceDetector.PlayerDeviceInfo deviceInfo = DeviceDetector.detectPlayerDevice(playerId, this);
        if (deviceInfo != null) {
            return deviceInfo.getFullDescription();
        }
        return isBedrockPlayer(playerId) ? "基岩版（未知设备）" : "Java版";
    }

    /**
     * 检查玩家是否适合使用表单界面
     * @param playerId 玩家UUID
     * @return 是否推荐使用表单界面
     */
    public boolean shouldUseFormInterface(UUID playerId) {
        if (!canSendForm(playerId)) {
            return false;
        }

        DeviceDetector.PlayerDeviceInfo deviceInfo = DeviceDetector.detectPlayerDevice(playerId, this);
        // 触屏设备更适合使用表单界面
        return deviceInfo != null && deviceInfo.isTouchInput();
    }

    /**
     * 检查Geyser是否启用
     * @return 如果Geyser API可用返回true
     */
    public boolean isGeyserEnabled() {
        return geyserEnabled;
    }

    /**
     * 检查Floodgate是否启用
     * @return 如果Floodgate API可用返回true
     */
    public boolean isFloodgateEnabled() {
        return floodgateEnabled;
    }

    /**
     * 检查是否有完整的跨平台支持
     * @return 如果Geyser和Floodgate都可用返回true
     */
    public boolean hasFullCrossPlatformSupport() {
        return geyserEnabled && floodgateEnabled;
    }

    /**
     * 创建队伍
     * @param teamName 队伍名称
     * @param leaderId 队长UUID
     * @return 创建的队伍，失败返回null
     */
    public Team createTeam(String teamName, UUID leaderId) {
        if (isTeamNameExists(teamName)) {
            return null;
        }

        if (getPlayerTeam(leaderId) != null) {
            return null; // 玩家已在队伍中
        }

        String teamId = UUID.randomUUID().toString();
        Team team = new Team(teamId, teamName, leaderId);

        teams.put(teamId, team);
        teamsByName.put(teamName.toLowerCase(), team);
        playerTeams.put(leaderId, teamId);

        return team;
    }

    /**
     * 检查队伍名称是否已存在
     * @param teamName 队伍名称
     * @return 是否存在
     */
    public boolean isTeamNameExists(String teamName) {
        return teamsByName.containsKey(teamName.toLowerCase());
    }

    /**
     * 获取玩家所在的队伍
     * @param playerId 玩家UUID
     * @return 队伍对象，不在队伍中返回null
     */
    public Team getPlayerTeam(UUID playerId) {
        String teamId = playerTeams.get(playerId);
        return teamId != null ? teams.get(teamId) : null;
    }

    /**
     * 根据ID或名称查找队伍
     * @param identifier 队伍ID或名称
     * @return 队伍对象，找不到返回null
     */
    public Team findTeam(String identifier) {
        // 先尝试按ID查找
        Team team = teams.get(identifier);
        if (team != null) {
            return team;
        }

        // 再尝试按名称查找
        return teamsByName.get(identifier.toLowerCase());
    }

    /**
     * 根据ID获取队伍
     * @param teamId 队伍ID
     * @return 队伍对象，找不到返回null
     */
    public Team getTeam(String teamId) {
        return teams.get(teamId);
    }

    /**
     * 加入队伍
     * @param playerId 玩家UUID
     * @param teamId 队伍ID
     * @param password 密码（如果需要）
     * @return 加入结果
     */
    public JoinResult joinTeam(UUID playerId, String teamId, String password) {
        Team team = teams.get(teamId);
        if (team == null) {
            return JoinResult.TEAM_NOT_FOUND;
        }

        if (team.isMember(playerId)) {
            return JoinResult.ALREADY_MEMBER;
        }

        if (team.isFull()) {
            return JoinResult.TEAM_FULL;
        }

        // 检查队伍状态和加入条件
        switch (team.getStatus()) {
            case PASSWORD_REQUIRED:
                if (!team.verifyPassword(password)) {
                    return JoinResult.WRONG_PASSWORD;
                }
                break;
            case INVITE_ONLY:
                return JoinResult.INVITE_ONLY;
            case APPLY_REQUIRED:
                return JoinResult.APPLY_REQUIRED;
            case OPEN:
                // 公开队伍，直接加入
                break;
        }

        // 加入队伍
        if (team.addMember(playerId)) {
            playerTeams.put(playerId, teamId);
            return JoinResult.SUCCESS;
        }

        return JoinResult.TEAM_FULL;
    }

    /**
     * 加入队伍（支持设备检查和警告提醒）
     * @param playerId 玩家UUID
     * @param teamId 队伍ID
     * @param password 密码（如果需要）
     * @param deviceManager 设备管理器
     * @return 加入结果
     */
    public JoinResult joinTeam(UUID playerId, String teamId, String password, mclmli.operationdiamondv2main.device.DeviceManager deviceManager) {
        Team team = teams.get(teamId);
        if (team == null) {
            return JoinResult.TEAM_NOT_FOUND;
        }

        if (team.isMember(playerId)) {
            return JoinResult.ALREADY_MEMBER;
        }

        if (team.isFull()) {
            return JoinResult.TEAM_FULL;
        }

        // 检查设备类型是否符合队伍限制
        if (!team.isDeviceAllowed(playerId, deviceManager)) {
            return JoinResult.DEVICE_NOT_ALLOWED;
        }

        // 检查队伍状态和加入条件
        switch (team.getStatus()) {
            case PASSWORD_REQUIRED:
                if (!team.verifyPassword(password)) {
                    return JoinResult.WRONG_PASSWORD;
                }
                break;
            case INVITE_ONLY:
                return JoinResult.INVITE_ONLY;
            case APPLY_REQUIRED:
                return JoinResult.APPLY_REQUIRED;
            case OPEN:
                // 公开队伍，直接加入
                break;
        }

        // 获取加入玩家的设备信息
        Player joiningPlayer = Bukkit.getPlayer(playerId);
        if (joiningPlayer == null) {
            // 玩家不在线，无法获取设备信息，但仍可以加入队伍
            // 加入队伍
            if (team.addMember(playerId)) {
                playerTeams.put(playerId, teamId);
                return JoinResult.SUCCESS;
            }
            return JoinResult.TEAM_FULL;
        }

        mclmli.operationdiamondv2main.device.DeviceDetector.PlayerDeviceInfo joiningPlayerDevice =
                deviceManager.getOrDetectPlayerDeviceInfo(joiningPlayer);

        // 检查是否会产生设备混合情况并发送警告
        boolean wasTouchscreenOnlyTeam = team.isTouchscreenOnlyTeam(deviceManager);

        // 加入队伍
        if (team.addMember(playerId)) {
            playerTeams.put(playerId, teamId);

            // 检查加入后是否产生了设备混合
            checkAndSendDeviceMixWarning(team, playerId, joiningPlayerDevice, wasTouchscreenOnlyTeam, deviceManager);

            return JoinResult.SUCCESS;
        }

        return JoinResult.TEAM_FULL;
    }

    /**
     * 检查并发送设备混合警告
     */
    private void checkAndSendDeviceMixWarning(Team team, UUID joiningPlayerId,
                                            mclmli.operationdiamondv2main.device.DeviceDetector.PlayerDeviceInfo joiningPlayerDevice,
                                            boolean wasTouchscreenOnlyTeam,
                                            mclmli.operationdiamondv2main.device.DeviceManager deviceManager) {
        if (joiningPlayerDevice == null) {
            return;
        }

        boolean joiningPlayerIsTouchscreen = joiningPlayerDevice.isTouchInput();
        boolean isTouchscreenOnlyTeamAfterJoin = team.isTouchscreenOnlyTeam(deviceManager);

        // 情况1：触屏玩家加入其他队伍（队伍中有非触屏玩家）
        if (joiningPlayerIsTouchscreen && !isTouchscreenOnlyTeamAfterJoin) {
            sendDeviceMixWarningToTeam(team, joiningPlayerId, true, deviceManager);
        }
        // 情况2：其他玩家加入触屏队伍（原本是纯触屏队伍）
        else if (!joiningPlayerIsTouchscreen && wasTouchscreenOnlyTeam) {
            sendDeviceMixWarningToTeam(team, joiningPlayerId, false, deviceManager);
        }
    }

    /**
     * 向队伍所有成员发送设备混合警告
     */
    private void sendDeviceMixWarningToTeam(Team team, UUID joiningPlayerId, boolean touchscreenJoiningOthers,
                                          mclmli.operationdiamondv2main.device.DeviceManager deviceManager) {
        String joiningPlayerName = org.bukkit.Bukkit.getOfflinePlayer(joiningPlayerId).getName();

        // 获取Player对象
        Player joiningPlayer = org.bukkit.Bukkit.getPlayer(joiningPlayerId);
        mclmli.operationdiamondv2main.device.DeviceDetector.PlayerDeviceInfo deviceInfo = null;
        if (joiningPlayer != null) {
            deviceInfo = deviceManager.getOrDetectPlayerDeviceInfo(joiningPlayer);
        }
        String joiningPlayerDevice = deviceInfo != null ? deviceInfo.getShortDescription() : "未知设备";

        for (UUID memberId : team.getMembers()) {
            org.bukkit.entity.Player member = org.bukkit.Bukkit.getPlayer(memberId);
            if (member == null || !member.isOnline()) {
                continue;
            }

            if (memberId.equals(joiningPlayerId)) {
                // 给加入的玩家特殊提醒
                if (touchscreenJoiningOthers) {
                    member.sendMessage("");
                    member.sendMessage(org.bukkit.ChatColor.YELLOW + "⚠️ 设备兼容性提醒:");
                    member.sendMessage(org.bukkit.ChatColor.GRAY + "你使用的是触屏设备，但队伍中有非触屏设备的玩家");
                    member.sendMessage(org.bukkit.ChatColor.GRAY + "匹配时你将与所有设备类型的玩家一起游戏");
                    member.sendMessage(org.bukkit.ChatColor.GRAY + "这可能会影响游戏体验的公平性");
                } else {
                    member.sendMessage("");
                    member.sendMessage(org.bukkit.ChatColor.YELLOW + "⚠️ 设备兼容性提醒:");
                    member.sendMessage(org.bukkit.ChatColor.GRAY + "你加入了包含触屏玩家的队伍");
                    member.sendMessage(org.bukkit.ChatColor.GRAY + "匹配时队伍将与所有设备类型的玩家一起游戏");
                }
                member.sendMessage("");
            } else {
                // 给其他队员提醒
                member.sendMessage("");
                member.sendMessage(org.bukkit.ChatColor.AQUA + "📱 队伍成员设备更新:");
                member.sendMessage(org.bukkit.ChatColor.WHITE + joiningPlayerName + " (" + joiningPlayerDevice + ") 加入了队伍");

                if (touchscreenJoiningOthers) {
                    member.sendMessage(org.bukkit.ChatColor.YELLOW + "⚠️ 现在队伍中包含了不同设备类型的玩家");
                    member.sendMessage(org.bukkit.ChatColor.GRAY + "匹配时将与所有设备类型的玩家一起游戏");
                } else {
                    member.sendMessage(org.bukkit.ChatColor.YELLOW + "⚠️ 队伍不再是纯触屏队伍");
                    member.sendMessage(org.bukkit.ChatColor.GRAY + "匹配时将与所有设备类型的玩家一起游戏");
                }
                member.sendMessage("");
            }
        }
    }

    /**
     * 获取所有队伍列表
     * @return 所有队伍的列表
     */
    public List<Team> getAllTeams() {
        return new ArrayList<>(teams.values());
    }

    /**
     * 获取所有公开的队伍列表（用于GUI显示）
     * @return 公开队伍的列表
     */
    public List<Team> getPublicTeams() {
        return teams.values().stream()
            .filter(team -> team.getStatus() == TeamStatus.OPEN ||
                          team.getStatus() == TeamStatus.APPLY_REQUIRED)
            .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 解散队伍
     * @param teamId 队伍ID
     * @return 是否成功解散
     */
    public boolean disbandTeam(String teamId) {
        Team team = teams.get(teamId);
        if (team == null) {
            return false;
        }

        // 移除所有成员的队伍映射
        for (UUID memberId : team.getMembers()) {
            playerTeams.remove(memberId);
        }

        // 移除队伍相关的邀请和申请
        cleanupTeamData(teamId);

        // 从映射中移除队伍
        teams.remove(teamId);
        teamsByName.remove(team.getTeamName().toLowerCase());

        return true;
    }

    /**
     * 清理队伍相关数据（邀请、申请等）
     */
    private void cleanupTeamData(String teamId) {
        // 清理相关邀请
        invitations.values().removeIf(invitation -> invitation.getTeamId().equals(teamId));

        // 清理相关申请
        applications.values().removeIf(application -> application.getTeamId().equals(teamId));
        teamApplications.remove(teamId);
    }

    /**
     * 从队伍中移除玩家
     * @param playerId 玩家UUID
     * @return 是否成功移除
     */
    public boolean removeFromTeam(UUID playerId) {
        String teamId = playerTeams.get(playerId);
        if (teamId == null) {
            return false;
        }

        Team team = teams.get(teamId);
        if (team == null) {
            return false;
        }

        if (team.removeMember(playerId)) {
            playerTeams.remove(playerId);

            // 如果队伍为空，解散队伍
            if (team.getMembers().isEmpty()) {
                disbandTeam(teamId);
            }

            return true;
        }

        return false;
    }


    /**
     * 创建邀请
     * @param teamId 队伍ID
     * @param inviterId 邀请者UUID
     * @param inviteeId 被邀请者UUID
     * @return 邀请对象，失败返回null
     */
    public TeamInvitation createInvitation(String teamId, UUID inviterId, UUID inviteeId) {
        // 检查是否已有待处理的邀请
        if (playerInvitations.containsKey(inviteeId)) {
            return null;
        }

        TeamInvitation invitation = new TeamInvitation(teamId, inviterId, inviteeId);
        invitations.put(invitation.getInvitationId(), invitation);
        playerInvitations.put(inviteeId, invitation.getInvitationId());

        return invitation;
    }

    /**
     * 获取玩家的邀请
     * @param playerId 玩家UUID
     * @return 邀请对象，没有邀请返回null
     */
    public TeamInvitation getPlayerInvitation(UUID playerId) {
        String invitationId = playerInvitations.get(playerId);
        return invitationId != null ? invitations.get(invitationId) : null;
    }

    /**
     * 移除邀请
     * @param invitationId 邀请ID
     * @return 是否成功移除
     */
    public boolean removeInvitation(String invitationId) {
        TeamInvitation invitation = invitations.remove(invitationId);
        if (invitation != null) {
            playerInvitations.remove(invitation.getInviteeId());
            return true;
        }
        return false;
    }

    /**
     * 创建申请
     * @param teamId 队伍ID
     * @param applicantId 申请者UUID
     * @param message 申请留言
     * @return 申请对象，失败返回null
     */
    public TeamApplication createApplication(String teamId, UUID applicantId, String message) {
        // 检查是否已有待处理的申请
        List<String> teamApps = teamApplications.get(teamId);
        if (teamApps != null) {
            for (String appId : teamApps) {
                TeamApplication app = applications.get(appId);
                if (app != null && app.getApplicantId().equals(applicantId) && app.isValid()) {
                    return null; // 已有待处理的申请
                }
            }
        }

        TeamApplication application = new TeamApplication(teamId, applicantId, message);
        applications.put(application.getApplicationId(), application);

        teamApplications.computeIfAbsent(teamId, k -> new ArrayList<>()).add(application.getApplicationId());

        return application;
    }

    /**
     * 获取队伍的申请
     * @param teamId 队伍ID
     * @param applicantId 申请者UUID
     * @return 申请对象，没有申请返回null
     */
    public TeamApplication getTeamApplication(String teamId, UUID applicantId) {
        List<String> teamApps = teamApplications.get(teamId);
        if (teamApps == null) {
            return null;
        }

        for (String appId : teamApps) {
            TeamApplication app = applications.get(appId);
            if (app != null && app.getApplicantId().equals(applicantId) && app.isValid()) {
                return app;
            }
        }

        return null;
    }

    /**
     * 移除申请
     * @param applicationId 申请ID
     * @return 是否成功移除
     */
    public boolean removeApplication(String applicationId) {
        TeamApplication application = applications.remove(applicationId);
        if (application != null) {
            List<String> teamApps = teamApplications.get(application.getTeamId());
            if (teamApps != null) {
                teamApps.remove(applicationId);
                if (teamApps.isEmpty()) {
                    teamApplications.remove(application.getTeamId());
                }
            }
            return true;
        }
        return false;
    }

    /**
     * 清理队伍的邀请和申请
     * @param teamId 队伍ID
     */
    private void cleanupTeamInvitationsAndApplications(String teamId) {
        // 清理邀请
        invitations.entrySet().removeIf(entry -> {
            TeamInvitation invitation = entry.getValue();
            if (invitation.getTeamId().equals(teamId)) {
                playerInvitations.remove(invitation.getInviteeId());
                return true;
            }
            return false;
        });

        // 清理申请
        List<String> teamApps = teamApplications.remove(teamId);
        if (teamApps != null) {
            for (String appId : teamApps) {
                applications.remove(appId);
            }
        }
    }

    /**
     * 检查是否支持发送表单给玩家
     * @param playerId 玩家UUID
     * @return 如果可以发送表单返回true
     */
    public boolean canSendForm(UUID playerId) {
        return isBedrockPlayer(playerId) && (geyserEnabled || floodgateEnabled);
    }

    /**
     * 定期清理过期的邀请和申请
     */
    public void cleanupExpired() {
        // 清理过期邀请
        invitations.entrySet().removeIf(entry -> {
            TeamInvitation invitation = entry.getValue();
            if (invitation.isExpired()) {
                playerInvitations.remove(invitation.getInviteeId());
                return true;
            }
            return false;
        });

        // 清理过期申请
        applications.entrySet().removeIf(entry -> {
            TeamApplication application = entry.getValue();
            if (application.isExpired()) {
                // 从队伍申请列表中移除
                List<String> teamApps = teamApplications.get(application.getTeamId());
                if (teamApps != null) {
                    teamApps.remove(application.getApplicationId());
                    if (teamApps.isEmpty()) {
                        teamApplications.remove(application.getTeamId());
                    }
                }
                return true;
            }
            return false;
        });
    }
}
