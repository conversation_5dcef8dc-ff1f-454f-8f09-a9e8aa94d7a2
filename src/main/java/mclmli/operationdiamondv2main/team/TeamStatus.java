package mclmli.operationdiamondv2main.team;

/**
 * 队伍状态枚举
 * 定义队伍的不同访问和加入模式
 */
public enum TeamStatus {
    /**
     * 公开 - 任何人都可以直接加入
     */
    OPEN("公开", "任何人都可以直接加入队伍"),

    /**
     * 需要申请 - 玩家申请后需要队长或管理员同意
     */
    APPLY_REQUIRED("需要申请", "玩家申请后需要队长同意才能加入"),

    /**
     * 仅邀请 - 只有被邀请的玩家才能加入
     */
    INVITE_ONLY("仅邀请", "只有被邀请的玩家才能加入队伍"),

    /**
     * 需要密码 - 玩家需要输入正确密码才能加入
     */
    PASSWORD_REQUIRED("需要密码", "玩家需要输入正确密码才能加入队伍");

    private final String displayName;
    private final String description;

    TeamStatus(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    /**
     * 获取状态显示名称
     * @return 显示名称
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * 获取状态描述
     * @return 状态描述
     */
    public String getDescription() {
        return description;
    }
}
